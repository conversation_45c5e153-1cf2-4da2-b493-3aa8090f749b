"""
Esquemas públicos para recomendaciones - Versión ofuscada para proteger IP.

SEGURIDAD: Estos esquemas ocultan la lógica de negocio interna y los detalles
de implementación de ML para prevenir ingeniería inversa por competidores.
"""

from enum import Enum
from pydantic import BaseModel, Field
from typing import Optional, Dict, Any, List
from .recommendation import FilterGroup, RecommendationContext


class PublicRecommendationGoal(str, Enum):
    """Objetivos de recomendación orientados al negocio (nombres ofuscados)"""
    CONVERSION_OPTIMIZATION = "conversion_optimization"  # Era "maximize_engagement"
    USER_ENGAGEMENT = "user_engagement"  # Era "balanced"
    DISCOVERY_MODE = "discovery_mode"  # Era "discover_diverse"
    CATALOG_PROMOTION = "catalog_promotion"  # Era "promote_new_arrivals"


class PublicModelVariant(str, Enum):
    """Variantes de modelo orientadas al plan de suscripción"""
    STANDARD = "standard"  # Modelo básico
    PREMIUM = "premium"   # Modelo avanzado
    ENTERPRISE = "enterprise"  # Modelo completo


class PublicExplanationLevel(str, Enum):
    """Niveles de explicación disponibles públicamente"""
    SIMPLE = "simple"
    # DETAILED eliminado de la versión pública


class PublicRecommendationQueryRequest(BaseModel):
    """
    Esquema público para solicitudes de recomendaciones.
    
    SEGURIDAD: Versión simplificada que oculta parámetros internos de ML
    y usa nombres de negocio en lugar de términos técnicos.
    """
    user_id: int = Field(..., gt=0, description="ID del usuario para el que se generan recomendaciones")
    filters: Optional[FilterGroup] = Field(None, description="Filtros para aplicar a las recomendaciones")
    context: Optional[RecommendationContext] = Field(None, description="Contexto para personalizar recomendaciones")
    recommendation_goal: Optional[PublicRecommendationGoal] = Field(
        PublicRecommendationGoal.USER_ENGAGEMENT, 
        description="Objetivo de la recomendación"
    )
    model_variant: PublicModelVariant = Field(
        PublicModelVariant.STANDARD, 
        description="Variante del modelo según su plan de suscripción"
    )
    include_explanation: bool = Field(False, description="Incluir explicación de las recomendaciones")
    explanation_level: PublicExplanationLevel = Field(
        PublicExplanationLevel.SIMPLE, 
        description="Nivel de detalle de la explicación"
    )
    skip: int = Field(0, ge=0, description="Número de elementos a omitir")
    limit: int = Field(10, ge=1, le=100, description="Número máximo de elementos a devolver")

    class Config:
        json_schema_extra = {
            "example": {
                "user_id": 123,
                "filters": {
                    "logic": "and",
                    "filters": [
                        {
                            "field": "price",
                            "op": "lt",
                            "value": 50
                        },
                        {
                            "field": "category",
                            "op": "eq",
                            "value": "electronics"
                        }
                    ]
                },
                "context": {
                    "page_type": "product_detail",
                    "device": "mobile",
                    "source_item_id": 456
                },
                "model_variant": "standard",
                "recommendation_goal": "user_engagement",
                "include_explanation": True,
                "skip": 0,
                "limit": 10
            }
        }


class PublicRecommendationQueryExternalRequest(BaseModel):
    """
    Esquema público para solicitudes usando external_user_id.
    
    SEGURIDAD: Versión simplificada sin exposición de parámetros internos.
    """
    external_user_id: str = Field(..., description="ID externo del usuario proporcionado por el cliente")
    filters: Optional[FilterGroup] = Field(None, description="Filtros para aplicar a las recomendaciones")
    context: Optional[RecommendationContext] = Field(None, description="Contexto para personalizar recomendaciones")
    recommendation_goal: Optional[PublicRecommendationGoal] = Field(
        PublicRecommendationGoal.USER_ENGAGEMENT, 
        description="Objetivo de la recomendación"
    )
    model_variant: PublicModelVariant = Field(
        PublicModelVariant.STANDARD, 
        description="Variante del modelo según su plan de suscripción"
    )
    include_explanation: bool = Field(False, description="Incluir explicación de las recomendaciones")
    explanation_level: PublicExplanationLevel = Field(
        PublicExplanationLevel.SIMPLE, 
        description="Nivel de detalle de la explicación"
    )
    skip: int = Field(0, ge=0, description="Número de elementos a omitir")
    limit: int = Field(10, ge=1, le=100, description="Número máximo de elementos a devolver")

    class Config:
        json_schema_extra = {
            "example": {
                "external_user_id": "user_abc123",
                "filters": {
                    "logic": "and",
                    "filters": [
                        {
                            "field": "price",
                            "op": "lt",
                            "value": 50
                        },
                        {
                            "field": "category",
                            "op": "eq",
                            "value": "electronics"
                        }
                    ]
                },
                "context": {
                    "page_type": "product_detail",
                    "device": "mobile",
                    "source_external_product_id": "prod_xyz789"
                },
                "model_variant": "standard",
                "recommendation_goal": "user_engagement",
                "include_explanation": True,
                "skip": 0,
                "limit": 10
            }
        }


class PublicSimpleExplanation(BaseModel):
    """
    Explicación simplificada para la versión pública.
    
    SEGURIDAD: No expone detalles internos del modelo ni métricas de confianza.
    """
    reason: str = Field(..., description="Razón principal de la recomendación")
    text_explanation: str = Field(..., description="Explicación en texto plano")

    class Config:
        json_schema_extra = {
            "example": {
                "reason": "similar_preferences",
                "text_explanation": "Recomendado basado en tus preferencias anteriores"
            }
        }


# Mapeo interno para convertir entre esquemas públicos y privados
PUBLIC_TO_INTERNAL_GOAL_MAPPING = {
    PublicRecommendationGoal.CONVERSION_OPTIMIZATION: "maximize_engagement",
    PublicRecommendationGoal.USER_ENGAGEMENT: "balanced", 
    PublicRecommendationGoal.DISCOVERY_MODE: "discover_diverse",
    PublicRecommendationGoal.CATALOG_PROMOTION: "promote_new_arrivals"
}

PUBLIC_TO_INTERNAL_MODEL_MAPPING = {
    PublicModelVariant.STANDARD: "standard",
    PublicModelVariant.PREMIUM: "premium", 
    PublicModelVariant.ENTERPRISE: "enterprise"
}


def convert_public_to_internal_request(public_request: PublicRecommendationQueryRequest) -> Dict[str, Any]:
    """
    Convierte una request pública a formato interno.
    
    SEGURIDAD: Esta función mantiene la separación entre la API pública
    y la lógica interna de ML.
    """
    internal_request = {
        "user_id": public_request.user_id,
        "filters": public_request.filters,
        "context": public_request.context,
        "strategy": PUBLIC_TO_INTERNAL_GOAL_MAPPING.get(
            public_request.recommendation_goal, 
            "balanced"
        ),
        "model_type": PUBLIC_TO_INTERNAL_MODEL_MAPPING.get(
            public_request.model_variant,
            "standard"
        ),
        "include_explanation": public_request.include_explanation,
        "explanation_level": public_request.explanation_level.value if public_request.explanation_level else "simple",
        "skip": public_request.skip,
        "limit": public_request.limit
    }
    
    return internal_request


def convert_public_external_to_internal_request(public_request: PublicRecommendationQueryExternalRequest) -> Dict[str, Any]:
    """
    Convierte una request externa pública a formato interno.
    """
    internal_request = {
        "external_user_id": public_request.external_user_id,
        "filters": public_request.filters,
        "context": public_request.context,
        "strategy": PUBLIC_TO_INTERNAL_GOAL_MAPPING.get(
            public_request.recommendation_goal, 
            "balanced"
        ),
        "model_type": PUBLIC_TO_INTERNAL_MODEL_MAPPING.get(
            public_request.model_variant,
            "standard"
        ),
        "include_explanation": public_request.include_explanation,
        "explanation_level": public_request.explanation_level.value if public_request.explanation_level else "simple",
        "skip": public_request.skip,
        "limit": public_request.limit
    }
    
    return internal_request
