"""
Dependencias centralizadas para la aplicación.
"""
import fastapi
from fastapi import Depends, HTTPException, status, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON>eader, OAuth2PasswordBearer
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Annotated, Type, TypeVar, Optional, Set, Any, Dict, Union
from datetime import datetime, timezone
import asyncio

from src.core.config import settings
from src.core.exceptions import LimitExceededError, InvalidAPIKeyError
from src.db.enums import PermissionType, RoleType
from src.db.models import SystemUser, Account
from src.core.permissions import get_required_permissions_for_resource
from src.db.repositories import (
    BaseRepository,
    AccountRepository,
    SystemUserRepository,
    ProductRepository,
)
from src.services import (
    SubscriptionService,
    PermissionService,
    LimitService,
    AuthService
)
from src.services.storage_tracker_service import StorageTrackerService
from src.services.batch_data_storage_service import BatchDataStorageService
from src.db.schemas import AccountResponse, SystemUser as SystemUserResponse
from src.db.session import get_db
from src.utils.base_logger import log_info, log_error

# Configuración de autenticación
api_key_header = APIKeyHeader(name="X-API-Key")
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")

# Type variable para repositorios
RepoType = TypeVar("RepoType", bound=BaseRepository)

# Cache de repositorios
def get_repo_cache_key(
    repo_class: Type[BaseRepository], account_id: Optional[str] | None
) -> str:
    return f"{repo_class.__name__}_{account_id}"

class RepositoryCacheEntry:
    def __init__(
        self, repository: BaseRepository, ttl_seconds: int = 300, max_size: int = 1000
    ):
        self.repository = repository
        self.created_at = datetime.utcnow()
        self.ttl_seconds = ttl_seconds
        self.max_size = max_size
        self.access_count = 0

    def is_expired(self) -> bool:
        self.access_count += 1
        return (
            datetime.utcnow() - self.created_at
        ).total_seconds() > self.ttl_seconds or self.access_count > self.max_size

async def get_cached_repository(
    request: Request,
    repo_class: Type[RepoType],
    account_id: Optional[str] | None,
    db: AsyncSession,
) -> RepoType:
    cache_key = get_repo_cache_key(repo_class, account_id)

    if not hasattr(request.state, "repo_cache"):
        request.state.repo_cache = {}

    cache_entry = request.state.repo_cache.get(cache_key)

    if cache_entry is None or cache_entry.is_expired():
        cache_entry = RepositoryCacheEntry(repo_class(db, account_id=account_id))
        request.state.repo_cache[cache_key] = cache_entry

    return cache_entry.repository

# Dependencias de autenticación
async def get_auth_service(db: AsyncSession = Depends(get_db)) -> AuthService:
    """Obtiene una instancia del servicio de autenticación."""
    return AuthService(db)

async def get_current_account(
    auth_service: AuthService = Depends(get_auth_service),
    token: Optional[str] = Depends(oauth2_scheme),
    api_key: Optional[str] = Depends(api_key_header)
) -> Account:
    """Obtiene la cuenta actual usando el servicio de autenticación."""
    return await auth_service.get_current_account(token=token, api_key=api_key)

async def get_current_system_user(
    auth_service: AuthService = Depends(get_auth_service),
    token: str = Depends(oauth2_scheme)
) -> SystemUser:
    """Obtiene el usuario del sistema actual usando el servicio de autenticación."""
    _, user = await auth_service.authenticate_with_jwt(token)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"}
        )
    return user

async def get_current_active_user(
    current_user: SystemUser = Depends(get_current_system_user)
) -> SystemUser:
    """Verifica que el usuario actual esté activo."""
    if not getattr(current_user, 'is_active', False):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Inactive user"
        )
    return current_user

# Dependencias de repositorios
async def get_account_repository(
    request: Request, db: AsyncSession = Depends(get_db)
) -> AccountRepository:
    return await get_cached_repository(request, AccountRepository, None, db)

async def get_system_user_repository(
    request: Request,
    db: AsyncSession = Depends(get_db),
    account: Account = Depends(get_current_account),
) -> SystemUserRepository:
    account_id = str(getattr(account, 'account_id'))
    return await get_cached_repository(
        request, SystemUserRepository, account_id, db
    )

# Dependencias de servicios
async def get_subscription_service(
    db: AsyncSession = Depends(get_db),
) -> SubscriptionService:
    return SubscriptionService(db)

async def get_permission_service(
    db: AsyncSession = Depends(get_db),
    account: Account = Depends(get_current_account),
) -> PermissionService:
    account_id = str(getattr(account, 'account_id'))
    return PermissionService(db, account_id)

async def get_current_admin_user(
    current_user: SystemUser = Depends(get_current_active_user),
    permission_service: PermissionService = Depends(get_permission_service)
) -> SystemUser:
    """Verifica que el usuario actual tenga rol de administrador."""
    # Verificar si el usuario tiene rol de administrador
    has_admin_role = await permission_service.has_role(current_user, RoleType.ADMIN)
    if not has_admin_role:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin privileges required"
        )
    return current_user

async def get_current_user_and_account(
    current_user: SystemUser = Depends(get_current_system_user),
    current_account: Account = Depends(get_current_account)
) -> tuple[SystemUser, Account]:
    """Obtiene tanto el usuario actual como la cuenta."""
    return current_user, current_account

async def get_limit_service(
    db: AsyncSession = Depends(get_db),
    account: Account = Depends(get_current_account),
) -> LimitService:
    account_id = str(getattr(account, 'account_id'))
    return LimitService(db, account_id)

async def get_storage_tracker_service(
    db: AsyncSession = Depends(get_db),
    account: Account = Depends(get_current_account),
) -> StorageTrackerService:
    account_id = str(getattr(account, 'account_id'))
    return StorageTrackerService(db, account_id)

async def get_batch_data_storage_service() -> BatchDataStorageService:
    """Obtiene una instancia del servicio de almacenamiento de datos de ingesta en lote."""
    return BatchDataStorageService()

# Dependencias de permisos y roles
async def require_roles(
    required_roles: Set[RoleType],
    current_user: Annotated[SystemUserResponse, Depends(get_current_active_user)],
) -> None:
    """Verifica que el usuario tenga los roles requeridos."""
    user_roles = set(getattr(current_user, 'roles', []))
    if not required_roles.issubset(user_roles):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )

async def check_permissions(
    current_user: SystemUser,
    required_permissions: Set[PermissionType],
    subscription_service: SubscriptionService = Depends(get_subscription_service),
) -> bool:
    """Verifica que el usuario tenga los permisos requeridos."""
    return await subscription_service.check_permissions(
        current_user, required_permissions
    )

async def require_permissions(
    permissions: Set[PermissionType],
    current_user: Annotated[SystemUser, Depends(get_current_active_user)],
    subscription_service: SubscriptionService = Depends(get_subscription_service),
) -> None:
    """Verifica que el usuario tenga los permisos requeridos."""
    if not await check_permissions(current_user, permissions, subscription_service):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )

# Dependencias de límites
async def check_account_limits(
    current_user: Annotated[SystemUser, Depends(get_current_active_user)],
    subscription_service: SubscriptionService = Depends(get_subscription_service),
) -> None:
    """Verifica los límites de la cuenta."""
    try:
        await subscription_service.check_limits(current_user)
    except LimitExceededError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )

async def check_storage_limits(
    current_user: Annotated[SystemUser, Depends(get_current_active_user)],
    storage_tracker: StorageTrackerService = Depends(get_storage_tracker_service),
    additional_bytes: int = 0,
) -> None:
    """Verifica los límites de almacenamiento."""
    try:
        await storage_tracker.check_storage_limits(
            current_user.account_id, additional_bytes
        )
    except LimitExceededError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )

# Dependencias de recursos
async def get_product_repository(
    request: Request,
    db: AsyncSession = Depends(get_db),
    account: Account = Depends(get_current_account),
) -> ProductRepository:
    account_id = str(getattr(account, 'account_id'))
    return await get_cached_repository(
        request, ProductRepository, account_id, db
    )

async def check_model_limits(
    current_user: Annotated[SystemUserResponse, Depends(get_current_active_user)],
    limit_service: LimitService = Depends(get_limit_service),
) -> None:
    """Verifica los límites de modelos."""
    try:
        await limit_service.check_model_limits(current_user)
    except LimitExceededError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )

# Dependencias de permisos de recursos
async def require_resource_permission(
    resource_type: str,
    action: str,
    current_user: Annotated[SystemUser, Depends(get_current_active_user)],
    permission_service: PermissionService = Depends(get_permission_service),
) -> None:
    """Verifica los permisos para un recurso específico."""
    required_permissions = get_required_permissions_for_resource(
        resource_type, action
    )
    if not await permission_service.check_permissions(
        current_user, required_permissions
    ):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )

# Dependencias específicas de recursos
async def require_product_read(
    current_user: Annotated[SystemUser, Depends(get_current_active_user)],
    permission_service: PermissionService = Depends(get_permission_service),
) -> None:
    await require_resource_permission("product", "read", current_user, permission_service)

async def require_product_create(
    current_user: Annotated[SystemUser, Depends(get_current_active_user)],
    permission_service: PermissionService = Depends(get_permission_service),
) -> None:
    await require_resource_permission("product", "create", current_user, permission_service)

async def require_product_update(
    current_user: Annotated[SystemUser, Depends(get_current_active_user)],
    permission_service: PermissionService = Depends(get_permission_service),
) -> None:
    await require_resource_permission("product", "update", current_user, permission_service)

async def require_product_delete(
    current_user: Annotated[SystemUser, Depends(get_current_active_user)],
    permission_service: PermissionService = Depends(get_permission_service),
) -> None:
    await require_resource_permission("product", "delete", current_user, permission_service)

async def require_user_read(
    current_user: Annotated[SystemUser, Depends(get_current_active_user)],
    permission_service: PermissionService = Depends(get_permission_service),
) -> None:
    await require_resource_permission("user", "read", current_user, permission_service)

async def require_user_create(
    current_user: Annotated[SystemUser, Depends(get_current_active_user)],
    permission_service: PermissionService = Depends(get_permission_service),
) -> None:
    await require_resource_permission("user", "create", current_user, permission_service)

async def require_user_update(
    current_user: Annotated[SystemUser, Depends(get_current_active_user)],
    permission_service: PermissionService = Depends(get_permission_service),
) -> None:
    await require_resource_permission("user", "update", current_user, permission_service)

async def require_user_delete(
    current_user: Annotated[SystemUser, Depends(get_current_active_user)],
    permission_service: PermissionService = Depends(get_permission_service),
) -> None:
    await require_resource_permission("user", "delete", current_user, permission_service)

async def require_system_user_read(
    current_user: Annotated[SystemUser, Depends(get_current_active_user)],
    permission_service: PermissionService = Depends(get_permission_service),
) -> None:
    await require_resource_permission("system_user", "read", current_user, permission_service)

async def require_system_user_create(
    current_user: Annotated[SystemUser, Depends(get_current_active_user)],
    permission_service: PermissionService = Depends(get_permission_service),
) -> None:
    await require_resource_permission("system_user", "create", current_user, permission_service)

async def require_system_user_update(
    current_user: Annotated[SystemUser, Depends(get_current_active_user)],
    permission_service: PermissionService = Depends(get_permission_service),
) -> None:
    await require_resource_permission("system_user", "update", current_user, permission_service)

async def require_system_user_delete(
    current_user: Annotated[SystemUser, Depends(get_current_active_user)],
    permission_service: PermissionService = Depends(get_permission_service),
) -> None:
    await require_resource_permission("system_user", "delete", current_user, permission_service)

async def require_role_management(
    current_user: Annotated[SystemUser, Depends(get_current_active_user)],
    permission_service: PermissionService = Depends(get_permission_service),
) -> None:
    await require_resource_permission("role", "manage", current_user, permission_service)

async def require_analytics_read(
    current_user: Annotated[SystemUser, Depends(get_current_active_user)],
    permission_service: PermissionService = Depends(get_permission_service),
) -> None:
    await require_resource_permission("analytics", "read", current_user, permission_service)

class APIKeyHeader(fastapi.security.APIKeyHeader):
    def __init__(
        self,
        *,
        name: str = "X-API-Key",
        scheme_name: Optional[str] = None,
        description: Optional[str] = None,
        auto_error: bool = True
    ):
        super().__init__(
            name=name,
            scheme_name=scheme_name,
            description=description or "Rayuela API Key, formato: ray_XXXXXXXXXXXXXXXXXXXX",
            auto_error=auto_error
        )

async def get_api_key_principal(
    api_key: str = Depends(api_key_header),
    db: AsyncSession = Depends(get_db)
) -> Account:
    """
    Dependencia para autenticar usando una API Key.
    Extrae la API Key del header X-API-Key y valida contra la base de datos.
    """
    try:
        from src.services.api_key_service import ApiKeyService
        
        # Usar el nuevo servicio de API Key para validar
        api_key_service = ApiKeyService(db)
        api_key_model = await api_key_service.validate_api_key(api_key)
        
        if not api_key_model:
            raise InvalidAPIKeyError()

        # Obtener la cuenta asociada
        account_repo = AccountRepository(db)
        account = await account_repo.get_by_id(api_key_model.account_id)
        
        if not account or not getattr(account, "is_active", False):
            raise InvalidAPIKeyError()

        return account
    except Exception as e:
        log_error(f"API Key authentication error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate API Key",
            headers={"WWW-Authenticate": "APIKey"},
        )
