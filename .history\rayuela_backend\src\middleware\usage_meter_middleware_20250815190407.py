"""
Middleware for tracking API calls and enforcing subscription limits.
"""
from fastapi import Request, Response, HTTPException, status
from fastapi.security import APIKeyHeader
from starlette.middleware.base import BaseHTTPMiddleware
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from datetime import datetime, timezone
import time
from typing import Optional, Dict, Any, Callable

from src.core.config import settings
from src.core.deps import get_db
from src.db.models import Subscription, Account
from src.db.enums import SubscriptionPlan, PLAN_LIMITS
from src.services.usage_meter_service import UsageMeterService
from src.core.exceptions import RateLimitExceededError, LimitExceededError
from src.utils.base_logger import log_info, log_error, log_warning
from src.core.cache_manager import CacheManager

# Use the same header as in deps.py
api_key_header = APIKeyHeader(name="X-API-Key", auto_error=False)


class UsageMeterMiddleware(BaseHTTPMiddleware):
    """
    Middleware for tracking API calls and enforcing subscription limits.
    This middleware uses the UsageMeterService to track API calls and enforce rate limits.
    """

    def __init__(self, app):
        super().__init__(app)
        self.excluded_paths = [
            "/docs",
            "/openapi.json",
            "/redoc",
            "/favicon.ico",
            "/health",
            "/api/v1/auth/token",  # Exclude auth endpoints
            "/api/v1/auth/register",
        ]
        self._cache_manager = CacheManager()  # Redis-based cache manager
        self._cache_ttl = 60  # 60 seconds TTL for the cache

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Main entrypoint for the middleware: authenticates, enforces limits and logs the call."""
        # Skip non-metered paths (docs, health, etc.)
        if self._should_skip(request):
            return await call_next(request)

        start_time = time.time()

        try:
            # 1) Authentication ────────────────────────────────────────────────
            api_key = self._authenticate_request(request)
            if api_key is None:  # Public endpoint – continue without metering
                return await call_next(request)

            # 2) DB session ─────────────────────────────────────────────────────
            db: AsyncSession = await self._get_db(request)

            # 3) Account & Subscription ─────────────────────────────────────────
            account, subscription = await self._get_account_and_subscription(db, api_key)

            # 4) Limits enforcement ─────────────────────────────────────────────
            usage_meter = UsageMeterService(db)
            await self._enforce_limits(usage_meter, account, subscription)

            # 5) Increment counter & forward request ────────────────────────────
            await usage_meter.increment_api_call(str(account.account_id))
            response = await call_next(request)

            # 6) Add rate-limit headers & log ───────────────────────────────────
            await self._add_rate_limit_headers(response, usage_meter, account, subscription)
            self._log_api_call(account, request, response, start_time)

            return response

        except HTTPException as http_exc:
            # Bubble up expected HTTP errors
            raise http_exc
        except Exception as e:
            # Unexpected errors → 500
            log_error(f"Error in usage meter middleware: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Internal server error",
            )

    # ────────────────────────────── Helper methods ─────────────────────────────

    def _should_skip(self, request: Request) -> bool:
        """Check if the request should skip API call counting."""
        path = request.url.path

        # Skip excluded paths
        if any(path.startswith(excluded) for excluded in self.excluded_paths):
            return True

        # Skip OPTIONS requests (CORS preflight)
        if request.method == "OPTIONS":
            return True

        return False

    def _authenticate_request(self, request: Request) -> Optional[str]:
        """Return the X-API-Key if required/available. Raise 401 if missing for protected paths.
        If the endpoint is public, returns None so the dispatcher can bypass metering.
        """
        api_key = request.headers.get("x-api-key")
        if api_key:
            return api_key

        # Define public endpoints that don't need API keys
        public_paths = [
            "/api/v1/auth",
            "/api/v1/health",
            "/api/v1/accounts",
            "/api/v1/plans",
            "/api/v1/public-openapi.json",  # SECURITY: Public OpenAPI endpoint for IP protection
        ]
        is_public = any(request.url.path.startswith(path) for path in public_paths)
        if request.url.path.startswith("/api/v1/") and not is_public:
            log_warning(
                f"Missing X-API-Key header for protected path: {request.url.path}"
            )
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="API Key is required",
            )
        # Public endpoint – no API key, no metering
        return None

    async def _get_db(self, request: Request) -> AsyncSession:
        """Ensure an AsyncSession is present in request.scope and return it."""
        db = request.scope.get("db")
        if db:
            return db
        db_gen = get_db()
        db = await anext(db_gen)
        request.scope["db"] = db
        return db

    async def _get_account_and_subscription(
        self, db: AsyncSession, api_key: str
    ) -> tuple[Account, Subscription]:
        """Retrieve account & active subscription or raise appropriate errors."""
        account = await self._get_account_from_api_key(db, api_key)
        if not account:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Invalid or inactive API Key",
            )

        subscription = await self._get_subscription(db, account.account_id)
        if not subscription or not subscription.is_active:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="No active subscription found",
            )
        return account, subscription

    async def _enforce_limits(
        self,
        usage_meter: UsageMeterService,
        account: Account,
        subscription: Subscription,
    ) -> None:
        """Check rate-limit and monthly quota, raising 429 if exceeded."""
        max_requests_per_minute = PLAN_LIMITS.get(subscription.plan_type, {}).get(
            "max_requests_per_minute", settings.DEFAULT_RATE_LIMIT
        )
        try:
            await usage_meter.check_rate_limit(str(account.account_id), max_requests_per_minute)
        except RateLimitExceededError as e:
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail=str(e),
                headers={"Retry-After": "60"},
            )

        try:
            await usage_meter.check_monthly_limit(
                str(account.account_id), subscription.api_calls_limit
            )
        except LimitExceededError:
            # Customise message for FREE plan users
            detail = "Monthly API call limit exceeded"
            if str(subscription.plan_type) == "FREE":
                detail = (
                    "Monthly API call limit exceeded. You have reached the limit of your "
                    "FREE plan. Please upgrade to a paid plan to continue using the API."
                )
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail=detail,
            )

    async def _add_rate_limit_headers(
        self,
        response: Response,
        usage_meter: UsageMeterService,
        account: Account,
        subscription: Subscription,
    ) -> None:
        """Populate X-RateLimit-* headers in the HTTP response."""
        try:
            response.headers["X-RateLimit-Limit"] = str(subscription.api_calls_limit)

            redis = await usage_meter.get_redis()
            monthly_key = usage_meter._get_monthly_counter_key(str(account.account_id))
            current_count = await redis.get(monthly_key)
            current_count = int(current_count) if current_count is not None else subscription.monthly_api_calls_used
            remaining = max(0, subscription.api_calls_limit - current_count)
            response.headers["X-RateLimit-Remaining"] = str(remaining)
        except Exception as e:
            log_warning(f"Error adding rate limit headers: {str(e)}")

        # Calculate first day UTC of next month for reset
        today = datetime.now(timezone.utc)
        next_month = (
            today.replace(year=today.year + 1, month=1, day=1)
            if today.month == 12
            else today.replace(month=today.month + 1, day=1)
        )
        response.headers["X-RateLimit-Reset"] = next_month.strftime("%Y-%m-%dT%H:%M:%SZ")

    def _log_api_call(
        self,
        account: Account,
        request: Request,
        response: Response,
        start_time: float,
    ) -> None:
        """Send a structured log entry with timing & status information."""
        log_info(
            "API call tracked",
            {
                "account_id": account.account_id,
                "path": request.url.path,
                "method": request.method,
                "response_time": time.time() - start_time,
                "status_code": response.status_code,
            },
        )

    async def _get_account_from_api_key(
        self, db: AsyncSession, api_key: str
    ) -> Optional[Account]:
        """Get account from API key with Redis caching and optimized direct lookup."""
        cache_key = f"account:{api_key}"

        # Check Redis cache first
        try:
            cached_account_data = await self._cache_manager.get(cache_key)
            if cached_account_data:
                # Reconstruct Account object from cached data
                account = Account()
                for key, value in cached_account_data.items():
                    if key in ["created_at", "updated_at", "deleted_at"] and value:
                        # Convert ISO string back to datetime
                        from datetime import datetime
                        setattr(account, key, datetime.fromisoformat(value))
                    else:
                        setattr(account, key, value)
                return account
        except Exception as e:
            log_warning(f"Error reading from cache for key {cache_key}: {str(e)}")

        # Optimized database query - direct lookup by API key hash
        try:
            from src.core.security.api_key import hash_api_key
            from src.db.repositories.api_key import ApiKeyRepository
            from src.db.repositories.account import AccountRepository

            # 1. Hash the provided API key
            api_key_hash = hash_api_key(api_key)

            # 2. Use the API key repository to find the API key directly by hash
            api_key_repo = ApiKeyRepository(db)
            api_key_model = await api_key_repo.get_by_api_key_hash(api_key_hash)

            if not api_key_model:
                return None  # API key not found or inactive

            # 3. Get the account associated with this API key
            account_repo = AccountRepository(db)
            account = await account_repo.get_by_id(api_key_model.account_id)

            if not account or not account.is_active or account.deleted_at is not None:
                return None  # Account not found, inactive, or deleted

            # Update Redis cache with account data
            try:
                account_data = {
                    "account_id": account.account_id,
                    "name": account.name,
                    "mercadopago_customer_id": account.mercadopago_customer_id,
                    "created_at": account.created_at.isoformat() if account.created_at else None,
                    "updated_at": account.updated_at.isoformat() if account.updated_at else None,
                    "is_active": account.is_active,
                    "deleted_at": account.deleted_at.isoformat() if account.deleted_at else None,
                }
                await self._cache_manager.set(cache_key, account_data, ttl=self._cache_ttl)
            except Exception as e:
                log_warning(f"Error updating cache for key {cache_key}: {str(e)}")

            # Update last_used timestamp for the API key (fire and forget)
            try:
                await api_key_repo.update_last_used(api_key_model.id)
            except Exception:
                # Don't fail authentication if we can't update last_used
                pass

            return account
        except Exception as e:
            log_error(f"Error getting account from API key: {str(e)}")
            return None

    async def _get_subscription(
        self, db: AsyncSession, account_id: int
    ) -> Optional[Subscription]:
        """Get subscription for an account with Redis caching."""
        cache_key = f"subscription:{account_id}"

        # Check Redis cache first
        try:
            cached_subscription_data = await self._cache_manager.get(cache_key)
            if cached_subscription_data:
                # Reconstruct Subscription object from cached data
                subscription = Subscription()
                for key, value in cached_subscription_data.items():
                    if key in ["created_at", "updated_at", "expires_at", "last_reset_date", "last_successful_training_at"] and value:
                        # Convert ISO string back to datetime
                        from datetime import datetime
                        setattr(subscription, key, datetime.fromisoformat(value))
                    elif key == "plan_type" and value:
                        # Convert string back to enum
                        from src.db.enums import SubscriptionPlan
                        setattr(subscription, key, SubscriptionPlan(value))
                    else:
                        setattr(subscription, key, value)
                return subscription
        except Exception as e:
            log_warning(f"Error reading from cache for key {cache_key}: {str(e)}")

        # Query database
        try:
            query = select(Subscription).where(
                Subscription.account_id == account_id,
                Subscription.is_active == True,
            )
            result = await db.execute(query)
            subscription = result.scalars().first()

            # Update Redis cache with subscription data
            if subscription:
                try:
                    subscription_data = {
                        "account_id": subscription.account_id,
                        "plan_type": subscription.plan_type.value if subscription.plan_type else None,
                        "api_calls_limit": subscription.api_calls_limit,
                        "storage_limit": subscription.storage_limit,
                        "training_frequency": str(subscription.training_frequency) if subscription.training_frequency else None,
                        "mercadopago_subscription_id": subscription.mercadopago_subscription_id,
                        "mercadopago_price_id": subscription.mercadopago_price_id,
                        "payment_gateway": subscription.payment_gateway,
                        "is_active": subscription.is_active,
                        "expires_at": subscription.expires_at.isoformat() if subscription.expires_at else None,
                        "created_at": subscription.created_at.isoformat() if subscription.created_at else None,
                        "updated_at": subscription.updated_at.isoformat() if subscription.updated_at else None,
                        "monthly_api_calls_used": subscription.monthly_api_calls_used,
                        "storage_used": subscription.storage_used,
                        "last_reset_date": subscription.last_reset_date.isoformat() if subscription.last_reset_date else None,
                        "available_models": subscription.available_models,
                        "last_successful_training_at": subscription.last_successful_training_at.isoformat() if subscription.last_successful_training_at else None,
                    }
                    await self._cache_manager.set(cache_key, subscription_data, ttl=self._cache_ttl)
                except Exception as e:
                    log_warning(f"Error updating cache for key {cache_key}: {str(e)}")

            return subscription
        except Exception as e:
            log_error(f"Error getting subscription: {str(e)}")
            return None
