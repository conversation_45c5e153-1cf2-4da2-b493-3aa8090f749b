"""
Esquemas públicos para pipeline - Versión ofuscada para proteger IP.

SEGURIDAD: Estos esquemas ocultan detalles de entrenamiento, parámetros
de modelos y métricas específicas que podrían revelar la implementación de ML.
"""

from pydantic import BaseModel, Field
from typing import Optional, Dict, Any, List
from datetime import datetime
from enum import Enum
from src.utils.camel_case import CamelCaseModel


class PublicTrainingStatus(str, Enum):
    """Estados públicos de entrenamiento sin detalles técnicos"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress" 
    COMPLETED = "completed"
    FAILED = "failed"


class PublicModelHealth(str, Enum):
    """Estados de salud del modelo orientados al negocio"""
    EXCELLENT = "excellent"
    GOOD = "good"
    FAIR = "fair"
    NEEDS_ATTENTION = "needs_attention"


class PublicTrainingResponse(CamelCaseModel):
    """
    Respuesta pública de entrenamiento sin detalles técnicos.
    
    SEGURIDAD: No expone job_id específicos ni detalles de la cola de tareas.
    """
    training_id: str = Field(..., description="ID público del entrenamiento")
    status: PublicTrainingStatus = Field(..., description="Estado del entrenamiento")
    estimated_completion: Optional[datetime] = Field(None, description="Tiempo estimado de finalización")
    message: str = Field(..., description="Mensaje descriptivo del estado")

    class Config:
        json_schema_extra = {
            "example": {
                "training_id": "train_abc123",
                "status": "in_progress",
                "estimated_completion": "2024-01-15T14:30:00Z",
                "message": "El modelo se está entrenando con los datos más recientes"
            }
        }


class PublicTrainingJobStatus(CamelCaseModel):
    """
    Estado público de trabajo de entrenamiento.
    
    SEGURIDAD: Versión simplificada sin métricas técnicas ni parámetros.
    """
    training_id: str = Field(..., description="ID público del entrenamiento")
    status: PublicTrainingStatus = Field(..., description="Estado actual")
    progress_percentage: int = Field(0, ge=0, le=100, description="Porcentaje de progreso")
    started_at: Optional[datetime] = Field(None, description="Fecha de inicio")
    estimated_completion: Optional[datetime] = Field(None, description="Tiempo estimado de finalización")
    completed_at: Optional[datetime] = Field(None, description="Fecha de finalización")
    message: str = Field("", description="Mensaje descriptivo")

    class Config:
        json_schema_extra = {
            "example": {
                "training_id": "train_abc123",
                "status": "in_progress",
                "progress_percentage": 65,
                "started_at": "2024-01-15T12:00:00Z",
                "estimated_completion": "2024-01-15T14:30:00Z",
                "completed_at": None,
                "message": "Procesando datos de interacciones recientes"
            }
        }


class PublicModelInfo(CamelCaseModel):
    """
    Información pública del modelo sin detalles técnicos.
    
    SEGURIDAD: No expone artifact_name, version específica ni parámetros.
    """
    model_id: str = Field(..., description="ID público del modelo")
    model_variant: str = Field(..., description="Variante del modelo (standard/premium/enterprise)")
    health_status: PublicModelHealth = Field(..., description="Estado de salud del modelo")
    training_date: datetime = Field(..., description="Fecha del último entrenamiento")
    data_points_used: int = Field(0, description="Cantidad de datos utilizados (aproximado)")
    description: Optional[str] = Field(None, description="Descripción del modelo")

    class Config:
        json_schema_extra = {
            "example": {
                "model_id": "model_xyz789",
                "model_variant": "premium",
                "health_status": "excellent",
                "training_date": "2024-01-10T02:00:00Z",
                "data_points_used": 50000,
                "description": "Modelo de recomendaciones personalizado para su catálogo"
            }
        }


class PublicModelPerformance(CamelCaseModel):
    """
    Rendimiento público del modelo sin métricas técnicas específicas.
    
    SEGURIDAD: Usa categorías de negocio en lugar de métricas ML precisas.
    """
    overall_performance: PublicModelHealth = Field(..., description="Rendimiento general")
    recommendation_quality: str = Field(..., description="Calidad de recomendaciones (High/Medium/Low)")
    user_satisfaction: str = Field(..., description="Satisfacción del usuario (Excellent/Good/Fair)")
    catalog_coverage: str = Field(..., description="Cobertura del catálogo (Comprehensive/Good/Limited)")
    response_speed: str = Field(..., description="Velocidad de respuesta (Fast/Normal/Slow)")
    last_evaluation: datetime = Field(..., description="Última evaluación")

    class Config:
        json_schema_extra = {
            "example": {
                "overall_performance": "excellent",
                "recommendation_quality": "High",
                "user_satisfaction": "Excellent",
                "catalog_coverage": "Comprehensive",
                "response_speed": "Fast",
                "last_evaluation": "2024-01-15T10:00:00Z"
            }
        }


class PublicModelSummary(CamelCaseModel):
    """
    Resumen público del modelo combinando info y rendimiento.
    
    SEGURIDAD: Vista consolidada sin exponer detalles técnicos.
    """
    model_info: PublicModelInfo = Field(..., description="Información del modelo")
    performance: PublicModelPerformance = Field(..., description="Rendimiento del modelo")

    class Config:
        json_schema_extra = {
            "example": {
                "model_info": {
                    "model_id": "model_xyz789",
                    "model_variant": "premium",
                    "health_status": "excellent",
                    "training_date": "2024-01-10T02:00:00Z",
                    "data_points_used": 50000,
                    "description": "Modelo de recomendaciones personalizado"
                },
                "performance": {
                    "overall_performance": "excellent",
                    "recommendation_quality": "High",
                    "user_satisfaction": "Excellent",
                    "catalog_coverage": "Comprehensive",
                    "response_speed": "Fast",
                    "last_evaluation": "2024-01-15T10:00:00Z"
                }
            }
        }


def convert_internal_to_public_training_response(internal_response: Dict[str, Any]) -> PublicTrainingResponse:
    """
    Convierte respuesta interna de entrenamiento a formato público.
    
    SEGURIDAD: Oculta job_id real y detalles de implementación.
    """
    # Generar ID público basado en timestamp para evitar exposición de IDs internos
    public_id = f"train_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    return PublicTrainingResponse(
        training_id=public_id,
        status=PublicTrainingStatus.PENDING,
        estimated_completion=datetime.now().replace(hour=datetime.now().hour + 2),
        message="El entrenamiento ha sido programado y comenzará pronto"
    )


def convert_internal_to_public_model_info(internal_model: Dict[str, Any]) -> PublicModelInfo:
    """
    Convierte información interna del modelo a formato público.
    
    SEGURIDAD: Abstrae detalles técnicos en información de negocio.
    """
    # Mapear health basado en métricas internas
    def determine_health(performance_metrics: Dict[str, Any]) -> PublicModelHealth:
        if not performance_metrics:
            return PublicModelHealth.FAIR
        
        # Usar métricas agregadas sin exponer valores específicos
        avg_performance = sum(performance_metrics.values()) / len(performance_metrics)
        
        if avg_performance >= 0.8:
            return PublicModelHealth.EXCELLENT
        elif avg_performance >= 0.6:
            return PublicModelHealth.GOOD
        elif avg_performance >= 0.4:
            return PublicModelHealth.FAIR
        else:
            return PublicModelHealth.NEEDS_ATTENTION
    
    # Generar ID público
    public_id = f"model_{internal_model.get('id', 'unknown')}"
    
    return PublicModelInfo(
        model_id=public_id,
        model_variant=internal_model.get("model_type", "standard"),
        health_status=determine_health(internal_model.get("performance_metrics", {})),
        training_date=internal_model.get("created_at", datetime.now()),
        data_points_used=internal_model.get("data_points", 0),
        description=internal_model.get("description", "Modelo de recomendaciones personalizado")
    )


def convert_internal_to_public_performance(internal_metrics: Dict[str, Any]) -> PublicModelPerformance:
    """
    Convierte métricas internas a rendimiento público.
    
    SEGURIDAD: Categoriza métricas técnicas en términos de negocio.
    """
    def categorize_metric(value: float, thresholds: tuple = (0.8, 0.6, 0.4)) -> str:
        high, medium, _ = thresholds
        if value >= high:
            return "High" if "quality" in str(thresholds) else "Excellent"
        elif value >= medium:
            return "Medium" if "quality" in str(thresholds) else "Good"
        else:
            return "Low" if "quality" in str(thresholds) else "Fair"
    
    def categorize_speed(response_time_ms: float) -> str:
        if response_time_ms <= 100:
            return "Fast"
        elif response_time_ms <= 300:
            return "Normal"
        else:
            return "Slow"
    
    # Extraer métricas con valores por defecto seguros
    precision = internal_metrics.get("precision", 0.0)
    recall = internal_metrics.get("recall", 0.0)
    coverage = internal_metrics.get("catalog_coverage", 0.0)
    response_time = internal_metrics.get("avg_response_time", 100.0)
    
    # Calcular rendimiento general
    overall_score = (precision + recall) / 2
    overall_health = PublicModelHealth.EXCELLENT if overall_score >= 0.8 else \
                    PublicModelHealth.GOOD if overall_score >= 0.6 else \
                    PublicModelHealth.FAIR if overall_score >= 0.4 else \
                    PublicModelHealth.NEEDS_ATTENTION
    
    return PublicModelPerformance(
        overall_performance=overall_health,
        recommendation_quality=categorize_metric(precision),
        user_satisfaction=categorize_metric(recall),
        catalog_coverage=categorize_metric(coverage),
        response_speed=categorize_speed(response_time),
        last_evaluation=datetime.now()
    )
