# Guía de Ingesta de Datos Masiva

Esta guía detalla cómo formatear y enviar datos a Rayuela utilizando el endpoint de ingesta masiva (`/ingestion/batch`). La correcta ingesta de datos es fundamental para obtener recomendaciones personalizadas de calidad.

## Contenido

1. [Visión General](#visión-general)
2. [Formato de Datos](#formato-de-datos)
   - [Usuarios (EndUsers)](#usuarios-endusers)
   - [Productos (Products)](#productos-products)
   - [Interacciones (Interactions)](#interacciones-interactions)
3. [Plantillas y Ejemplos](#plantillas-y-ejemplos)
   - [Plantillas JSON](#plantillas-json)
   - [Plantillas CSV](#plantillas-csv)
   - [Ejemplos Completos](#ejemplos-completos)
4. [Envío de Datos](#envío-de-datos)
   - [Usando cURL](#usando-curl)
   - [Usando Python](#usando-python)
   - [Usando Node.js](#usando-nodejs)
   - [Usando PHP](#usando-php)
5. [Monitoreo del Proceso](#monitoreo-del-proceso)
   - [Verificación del Estado](#verificación-del-estado)
   - [Interpretación de Errores](#interpretación-de-errores)
6. [Manejo de Errores Comunes](#manejo-de-errores-comunes)
7. [Mejores Prácticas](#mejores-prácticas)
8. [Preguntas Frecuentes](#preguntas-frecuentes)

## Visión General

El endpoint de ingesta masiva (`POST /api/v1/ingestion/batch`) permite cargar grandes volúmenes de datos en una sola operación. Este proceso es asíncrono, lo que significa que la API responderá inmediatamente con un ID de trabajo, mientras el procesamiento continúa en segundo plano.

### Ventajas de la Ingesta Masiva

- **Eficiencia**: Carga miles de registros en una sola llamada API
- **Consistencia**: Garantiza la integridad referencial entre entidades
- **Rendimiento**: Optimizado para grandes volúmenes de datos
- **Monitoreo**: Proporciona información detallada sobre el progreso y errores
- **Seguridad**: Los datos se almacenan de forma segura durante el procesamiento

### Flujo de Trabajo

1. **Preparación**: Formatee sus datos según las especificaciones
2. **Envío**: Realice una solicitud POST al endpoint de ingesta
3. **Confirmación**: Reciba un ID de trabajo para monitoreo
4. **Monitoreo**: Consulte el estado del trabajo periódicamente
5. **Verificación**: Confirme que todos los datos se procesaron correctamente

## Formato de Datos

La solicitud de ingesta masiva acepta tres tipos de entidades:

### Usuarios (EndUsers)

Los usuarios finales representan a las personas que interactúan con los productos en su sistema.

**Campos requeridos:**
- `external_id`: Identificador único del usuario en su sistema (string, 1-255 caracteres)

**Validaciones:**
- El `external_id` debe ser único dentro de su cuenta
- No puede estar vacío o contener solo espacios en blanco
- Se recomienda usar identificadores alfanuméricos sin caracteres especiales

**Ejemplo mínimo:**
```json
{
  "externalId": "user123"
}
```

**Ejemplo con mejores prácticas:**
```json
{
  "externalId": "usr_12345_abc",
  "preferredCategories": ["electronics", "books"],
  "priceRangeMin": 10.50,
  "priceRangeMax": 500.00,
  "demographicInfo": {
    "age": 28,
    "gender": "M"
  }
}
```

### Productos (Products)

Los productos representan los ítems que pueden ser recomendados a los usuarios.

**Campos requeridos:**
- `name`: Nombre del producto (string, 1-255 caracteres)
- `price`: Precio del producto (decimal > 0, máximo 10 dígitos con 2 decimales)
- `category`: Categoría del producto (string, 1-100 caracteres)

**Campos opcionales:**
- `description`: Descripción del producto (string, sin límite de longitud)
- `average_rating`: Calificación promedio (float entre 0.0 y 5.0)
- `num_ratings`: Número de calificaciones (integer ≥ 0)
- `inventory_count`: Cantidad en inventario (integer ≥ 0)

**Validaciones:**
- El precio debe ser mayor que 0
- La calificación promedio debe estar entre 0.0 y 5.0
- Los contadores no pueden ser negativos

**Ejemplo mínimo:**
```json
{
  "name": "Smartphone XYZ",
  "price": 499.99,
  "category": "electronics"
}
```

**Ejemplo completo:**
```json
{
  "name": "Smartphone XYZ Pro",
  "description": "Un smartphone de última generación con pantalla OLED de 6.7 pulgadas, cámara triple de 108MP y batería de 5000mAh",
  "price": 899.99,
  "category": "electronics",
  "average_rating": 4.5,
  "num_ratings": 1247,
  "inventory_count": 150
}
```

### Interacciones (Interactions)

Las interacciones representan las acciones que los usuarios realizan con los productos.

**Campos requeridos:**
- `interaction_type`: Tipo de interacción (enum, ver tipos válidos abajo)
- `value`: Valor asociado a la interacción (decimal)

**Identificación de usuarios y productos (elige una opción):**
- **Opción 1 (Recomendada): IDs Externos**
  - `external_user_id`: ID externo del usuario (string, tu propio identificador)
  - `external_product_id`: ID externo del producto (string, tu propio identificador)
- **Opción 2: IDs Internos**
  - `user_id`: ID numérico interno del usuario (integer, debe existir en la base de datos)
  - `product_id`: ID numérico interno del producto (integer, debe existir en la base de datos)

**Tipos de interacción válidos:**
- `VIEW`: El usuario vio el producto (value: 1.0)
- `LIKE`: El usuario indicó que le gusta el producto (value: 1.0)
- `PURCHASE`: El usuario compró el producto (value: cantidad o 1.0)
- `CART`: El usuario añadió el producto al carrito (value: cantidad o 1.0)
- `RATING`: El usuario calificó el producto (value: calificación 1.0-5.0)
- `WISHLIST`: El usuario añadió el producto a su lista de deseos (value: 1.0)
- `CLICK`: El usuario hizo clic en el producto (value: 1.0)
- `SEARCH`: El usuario encontró el producto a través de una búsqueda (value: 1.0)
- `FAVORITE`: El usuario marcó el producto como favorito (value: 1.0)

**Validaciones:**
- Debes proporcionar **o bien** IDs externos **o bien** IDs internos, pero no ambos
- Los IDs externos se resuelven automáticamente a IDs internos
- El tipo de interacción debe ser uno de los valores válidos
- Para RATING, el valor debe estar entre 1.0 y 5.0
- Para otros tipos, el valor típicamente es 1.0 o la cantidad

**Ejemplo usando IDs externos (recomendado):**
```json
{
  "external_user_id": "user_12345",
  "external_product_id": "product_abc",
  "interaction_type": "PURCHASE",
  "value": 2.0
}
```

**Ejemplo usando IDs internos:**
```json
{
  "user_id": 1,
  "product_id": 42,
  "interaction_type": "PURCHASE",
  "value": 2.0
}
```

## Plantillas y Ejemplos

### Plantillas JSON

#### Plantilla Básica
```json
{
  "users": [
    {"external_id": "REEMPLAZAR_CON_ID_USUARIO"}
  ],
  "products": [
    {
      "name": "REEMPLAZAR_CON_NOMBRE",
      "price": 0.00,
      "category": "REEMPLAZAR_CON_CATEGORIA"
    }
  ],
  "interactions": [
    {
      "external_user_id": "REEMPLAZAR_CON_ID_USUARIO",
      "external_product_id": "REEMPLAZAR_CON_ID_PRODUCTO",
      "interaction_type": "VIEW",
      "value": 1.0
    }
  ]
}
```

#### Plantilla Completa
```json
{
  "users": [
    {
      "externalId": "user001",
      "preferredCategories": ["electronics", "books"],
      "priceRangeMin": 10.50,
      "priceRangeMax": 500.00
    },
    {
      "externalId": "user002",
      "preferredCategories": ["home", "garden"],
      "priceRangeMin": 20.00,
      "priceRangeMax": 300.00
    },
    {"externalId": "user003"}
  ],
  "products": [
    {
      "externalId": "prod001",
      "name": "Producto Ejemplo",
      "description": "Descripción detallada del producto",
      "price": 99.99,
      "category": "electronics",
      "averageRating": 4.2,
      "numRatings": 85,
      "inventoryCount": 100
    }
  ],
  "interactions": [
    {
      "externalUserId": "user001",
      "externalProductId": "prod001",
      "interactionType": "VIEW",
      "value": 1.0,
      "recommendationMetadata": {
        "source": "homepage",
        "algorithm": "collaborative_filtering"
      }
    },
    {
      "externalUserId": "user001",
      "externalProductId": "prod001",
      "interactionType": "PURCHASE",
      "value": 1.0,
      "recommendationMetadata": {
        "source": "product_page",
        "algorithm": "content_based"
      }
    }
  ]
}
```

### Plantillas CSV

#### users.csv
```csv
external_id
user001
user002
user003
```

#### products.csv (Mínimo)
```csv
name,price,category
Smartphone XYZ,499.99,electronics
Laptop ABC,1299.99,electronics
Auriculares Bluetooth,89.99,electronics
```

#### products.csv (Completo)
```csv
name,description,price,category,average_rating,num_ratings,inventory_count
Smartphone XYZ,Un smartphone de última generación,499.99,electronics,4.5,120,50
Laptop ABC,Laptop para profesionales,1299.99,electronics,4.8,85,30
Auriculares Bluetooth,Auriculares inalámbricos,89.99,electronics,4.2,210,75
```

#### interactions.csv
```csv
external_user_id,external_product_id,interaction_type,value
user001,product001,VIEW,1.0
user001,product001,PURCHASE,1.0
user002,product001,RATING,4.5
user002,product002,VIEW,1.0
user003,product001,LIKE,1.0
```

### Ejemplos Completos

#### Ejemplo E-commerce
```json
{
  "users": [
    {"external_id": "customer_001"},
    {"external_id": "customer_002"},
    {"external_id": "customer_003"}
  ],
  "products": [
    {
      "name": "iPhone 15 Pro",
      "description": "El iPhone más avanzado con chip A17 Pro y cámara de 48MP",
      "price": 1199.99,
      "category": "smartphones",
      "average_rating": 4.7,
      "num_ratings": 2341,
      "inventory_count": 50
    },
    {
      "name": "MacBook Air M2",
      "description": "Laptop ultradelgada con chip M2 y pantalla Liquid Retina",
      "price": 1399.99,
      "category": "laptops",
      "average_rating": 4.8,
      "num_ratings": 1876,
      "inventory_count": 25
    }
  ],
  "interactions": [
    {
      "user_id": 1,
      "product_id": 1,
      "interaction_type": "VIEW",
      "value": 1.0
    },
    {
      "user_id": 1,
      "product_id": 1,
      "interaction_type": "CART",
      "value": 1.0
    },
    {
      "user_id": 1,
      "product_id": 1,
      "interaction_type": "PURCHASE",
      "value": 1.0
    },
    {
      "user_id": 2,
      "product_id": 2,
      "interaction_type": "VIEW",
      "value": 1.0
    },
    {
      "user_id": 2,
      "product_id": 2,
      "interaction_type": "RATING",
      "value": 5.0
    }
  ]
}
```

#### Ejemplo Streaming/Media
```json
{
  "users": [
    {"external_id": "viewer_001"},
    {"external_id": "viewer_002"}
  ],
  "products": [
    {
      "name": "Película: Avengers Endgame",
      "description": "Épica conclusión de la saga del infinito",
      "price": 4.99,
      "category": "movies",
      "average_rating": 4.9,
      "num_ratings": 15420,
      "inventory_count": 999999
    },
    {
      "name": "Serie: Stranger Things T4",
      "description": "Cuarta temporada de la popular serie de Netflix",
      "price": 0.00,
      "category": "series",
      "average_rating": 4.6,
      "num_ratings": 8765,
      "inventory_count": 999999
    }
  ],
  "interactions": [
    {
      "user_id": 1,
      "product_id": 1,
      "interaction_type": "VIEW",
      "value": 1.0
    },
    {
      "user_id": 1,
      "product_id": 1,
      "interaction_type": "RATING",
      "value": 5.0
    },
    {
      "user_id": 2,
      "product_id": 2,
      "interaction_type": "VIEW",
      "value": 1.0
    }
  ]
}
```

> **Nota**: El sistema calcula automáticamente el tamaño de los datos para validar los límites de almacenamiento de su plan.

## Envío de Datos

### Usando cURL

#### Ejemplo Básico
```bash
curl -X POST "https://api.rayuela.ai/api/v1/ingestion/batch" \
  -H "X-API-Key: tu_api_key" \
  -H "Content-Type: application/json" \
  -d @payload.json
```

#### Ejemplo con Manejo de Errores
```bash
#!/bin/bash

API_KEY="tu_api_key"
API_URL="https://api.rayuela.ai/api/v1/ingestion/batch"
PAYLOAD_FILE="payload.json"

# Verificar que el archivo existe
if [ ! -f "$PAYLOAD_FILE" ]; then
    echo "Error: El archivo $PAYLOAD_FILE no existe"
    exit 1
fi

# Realizar la solicitud
response=$(curl -s -w "%{http_code}" -X POST "$API_URL" \
  -H "X-API-Key: $API_KEY" \
  -H "Content-Type: application/json" \
  -d @"$PAYLOAD_FILE")

# Extraer código de estado y cuerpo de respuesta
http_code="${response: -3}"
body="${response%???}"

# Verificar el resultado
if [ "$http_code" -eq 202 ]; then
    echo "✅ Ingesta iniciada correctamente"
    echo "$body" | jq '.'
    
    # Extraer job_id para monitoreo
    job_id=$(echo "$body" | jq -r '.job_id')
    echo "🔍 Monitorea el progreso con: curl -H 'X-API-Key: $API_KEY' '$API_URL/$job_id'"
else
    echo "❌ Error en la ingesta (HTTP $http_code)"
    echo "$body" | jq '.'
    exit 1
fi
```

### Usando Python

#### Ejemplo Básico
```python
import requests
import json

# Configuración
API_KEY = "tu_api_key"
API_URL = "https://api.rayuela.ai/api/v1/ingestion/batch"

# Cargar datos
with open("payload.json", "r", encoding="utf-8") as f:
    payload = json.load(f)

# Enviar solicitud
headers = {
    "X-API-Key": API_KEY,
    "Content-Type": "application/json"
}

response = requests.post(API_URL, headers=headers, json=payload)

# Verificar respuesta
if response.status_code == 202:  # Accepted
    result = response.json()
    job_id = result["job_id"]
    print(f"✅ Ingesta iniciada correctamente. Job ID: {job_id}")
    print(f"📊 Total usuarios: {result['total_users']}")
    print(f"📦 Total productos: {result['total_products']}")
    print(f"🔗 Total interacciones: {result['total_interactions']}")
else:
    print(f"❌ Error: {response.status_code}")
    print(response.text)
```

#### Ejemplo Completo con Monitoreo
```python
import requests
import json
import time
from typing import Dict, Any

class RayuelaIngestionClient:
    def __init__(self, api_key: str, base_url: str = "https://api.rayuela.ai/api/v1"):
        self.api_key = api_key
        self.base_url = base_url
        self.headers = {
            "X-API-Key": api_key,
            "Content-Type": "application/json"
        }
    
    def start_batch_ingestion(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Inicia un trabajo de ingesta masiva."""
        url = f"{self.base_url}/ingestion/batch"
        response = requests.post(url, headers=self.headers, json=data)
        
        if response.status_code == 202:
            return response.json()
        else:
            response.raise_for_status()
    
    def get_job_status(self, job_id: int) -> Dict[str, Any]:
        """Obtiene el estado de un trabajo de ingesta."""
        url = f"{self.base_url}/ingestion/batch/{job_id}"
        response = requests.get(url, headers=self.headers)
        
        if response.status_code == 200:
            return response.json()
        else:
            response.raise_for_status()
    
    def wait_for_completion(self, job_id: int, max_wait_time: int = 300) -> Dict[str, Any]:
        """Espera a que se complete un trabajo de ingesta."""
        start_time = time.time()
        
        while time.time() - start_time < max_wait_time:
            status = self.get_job_status(job_id)
            
            print(f"Estado: {status['status']}")
            if status.get('progress_percentage'):
                print(f"Progreso: {status['progress_percentage']:.1f}%")
            
            if status['status'] in ['COMPLETED', 'FAILED']:
                return status
            
            time.sleep(5)  # Esperar 5 segundos antes de la siguiente consulta
        
        raise TimeoutError(f"El trabajo {job_id} no se completó en {max_wait_time} segundos")

# Uso del cliente
if __name__ == "__main__":
    client = RayuelaIngestionClient("tu_api_key")
    
    # Datos de ejemplo
    data = {
        "users": [{"external_id": "user001"}],
        "products": [{
            "name": "Producto Test",
            "price": 99.99,
            "category": "test"
        }],
        "interactions": [{
            "user_id": 1,
            "product_id": 1,
            "interaction_type": "VIEW",
            "value": 1.0
        }]
    }
    
    try:
        # Iniciar ingesta
        result = client.start_batch_ingestion(data)
        job_id = result["job_id"]
        print(f"✅ Ingesta iniciada. Job ID: {job_id}")
        
        # Esperar a que se complete
        final_status = client.wait_for_completion(job_id)
        
        if final_status['status'] == 'COMPLETED':
            print("🎉 Ingesta completada exitosamente!")
            if 'processed_count' in final_status:
                counts = final_status['processed_count']
                print(f"📊 Procesados: {counts}")
        else:
            print(f"❌ Ingesta falló: {final_status.get('error_message', 'Error desconocido')}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
```

### Usando Node.js

#### Ejemplo Básico
```javascript
const axios = require('axios');
const fs = require('fs');

// Configuración
const API_KEY = 'tu_api_key';
const API_URL = 'https://api.rayuela.ai/api/v1/ingestion/batch';

// Cargar datos
const payload = JSON.parse(fs.readFileSync('payload.json', 'utf8'));

// Enviar solicitud
axios.post(API_URL, payload, {
  headers: {
    'X-API-Key': API_KEY,
    'Content-Type': 'application/json'
  }
})
.then(response => {
  const result = response.data;
  console.log(`✅ Ingesta iniciada correctamente. Job ID: ${result.job_id}`);
  console.log(`📊 Total usuarios: ${result.total_users}`);
  console.log(`📦 Total productos: ${result.total_products}`);
  console.log(`🔗 Total interacciones: ${result.total_interactions}`);
})
.catch(error => {
  console.error('❌ Error:', error.response ? error.response.status : error.message);
  if (error.response) {
    console.error(error.response.data);
  }
});
```

#### Ejemplo Completo con Clase
```javascript
const axios = require('axios');

class RayuelaIngestionClient {
  constructor(apiKey, baseUrl = 'https://api.rayuela.ai/api/v1') {
    this.apiKey = apiKey;
    this.baseUrl = baseUrl;
    this.headers = {
      'X-API-Key': apiKey,
      'Content-Type': 'application/json'
    };
  }

  async startBatchIngestion(data) {
    try {
      const response = await axios.post(
        `${this.baseUrl}/ingestion/batch`,
        data,
        { headers: this.headers }
      );
      return response.data;
    } catch (error) {
      throw new Error(`Error iniciando ingesta: ${error.response?.data?.detail || error.message}`);
    }
  }

  async getJobStatus(jobId) {
    try {
      const response = await axios.get(
        `${this.baseUrl}/ingestion/batch/${jobId}`,
        { headers: this.headers }
      );
      return response.data;
    } catch (error) {
      throw new Error(`Error obteniendo estado: ${error.response?.data?.detail || error.message}`);
    }
  }

  async waitForCompletion(jobId, maxWaitTime = 300000) { // 5 minutos por defecto
    const startTime = Date.now();
    
    while (Date.now() - startTime < maxWaitTime) {
      const status = await this.getJobStatus(jobId);
      
      console.log(`Estado: ${status.status}`);
      if (status.progress_percentage) {
        console.log(`Progreso: ${status.progress_percentage.toFixed(1)}%`);
      }
      
      if (['COMPLETED', 'FAILED'].includes(status.status)) {
        return status;
      }
      
      await new Promise(resolve => setTimeout(resolve, 5000)); // Esperar 5 segundos
    }
    
    throw new Error(`El trabajo ${jobId} no se completó en ${maxWaitTime/1000} segundos`);
  }
}

// Uso del cliente
async function main() {
  const client = new RayuelaIngestionClient('tu_api_key');
  
  const data = {
    users: [{ external_id: 'user001' }],
    products: [{
      name: 'Producto Test',
      price: 99.99,
      category: 'test'
    }],
    interactions: [{
      user_id: 1,
      product_id: 1,
      interaction_type: 'VIEW',
      value: 1.0
    }]
  };
  
  try {
    // Iniciar ingesta
    const result = await client.startBatchIngestion(data);
    console.log(`✅ Ingesta iniciada. Job ID: ${result.job_id}`);
    
    // Esperar a que se complete
    const finalStatus = await client.waitForCompletion(result.job_id);
    
    if (finalStatus.status === 'COMPLETED') {
      console.log('🎉 Ingesta completada exitosamente!');
      if (finalStatus.processed_count) {
        console.log('📊 Procesados:', finalStatus.processed_count);
      }
    } else {
      console.log(`❌ Ingesta falló: ${finalStatus.error_message || 'Error desconocido'}`);
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

main();
```

### Usando PHP

#### Ejemplo Básico
```php
<?php

// Configuración
$apiKey = 'tu_api_key';
$apiUrl = 'https://api.rayuela.ai/api/v1/ingestion/batch';

// Cargar datos
$payload = json_decode(file_get_contents('payload.json'), true);

// Configurar cURL
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $apiUrl);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($payload));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'X-API-Key: ' . $apiKey,
    'Content-Type: application/json'
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

// Ejecutar solicitud
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

// Verificar respuesta
if ($httpCode === 202) {
    $result = json_decode($response, true);
    echo "✅ Ingesta iniciada correctamente. Job ID: " . $result['job_id'] . "\n";
    echo "📊 Total usuarios: " . $result['total_users'] . "\n";
    echo "📦 Total productos: " . $result['total_products'] . "\n";
    echo "🔗 Total interacciones: " . $result['total_interactions'] . "\n";
} else {
    echo "❌ Error: HTTP $httpCode\n";
    echo $response . "\n";
}
?>
```

#### Ejemplo Completo con Clase
```php
<?php

class RayuelaIngestionClient {
    private $apiKey;
    private $baseUrl;
    
    public function __construct($apiKey, $baseUrl = 'https://api.rayuela.ai/api/v1') {
        $this->apiKey = $apiKey;
        $this->baseUrl = $baseUrl;
    }
    
    public function startBatchIngestion($data) {
        $url = $this->baseUrl . '/ingestion/batch';
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'X-API-Key: ' . $this->apiKey,
            'Content-Type: application/json'
        ]);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode === 202) {
            return json_decode($response, true);
        } else {
            throw new Exception("Error iniciando ingesta: HTTP $httpCode - $response");
        }
    }
    
    public function getJobStatus($jobId) {
        $url = $this->baseUrl . "/ingestion/batch/$jobId";
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'X-API-Key: ' . $this->apiKey
        ]);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode === 200) {
            return json_decode($response, true);
        } else {
            throw new Exception("Error obteniendo estado: HTTP $httpCode - $response");
        }
    }
    
    public function waitForCompletion($jobId, $maxWaitTime = 300) {
        $startTime = time();
        
        while (time() - $startTime < $maxWaitTime) {
            $status = $this->getJobStatus($jobId);
            
            echo "Estado: " . $status['status'] . "\n";
            if (isset($status['progress_percentage'])) {
                echo "Progreso: " . number_format($status['progress_percentage'], 1) . "%\n";
            }
            
            if (in_array($status['status'], ['COMPLETED', 'FAILED'])) {
                return $status;
            }
            
            sleep(5); // Esperar 5 segundos
        }
        
        throw new Exception("El trabajo $jobId no se completó en $maxWaitTime segundos");
    }
}

// Uso del cliente
try {
    $client = new RayuelaIngestionClient('tu_api_key');
    
    $data = [
        'users' => [['external_id' => 'user001']],
        'products' => [[
            'name' => 'Producto Test',
            'price' => 99.99,
            'category' => 'test'
        ]],
        'interactions' => [[
            'user_id' => 1,
            'product_id' => 1,
            'interaction_type' => 'VIEW',
            'value' => 1.0
        ]]
    ];
    
    // Iniciar ingesta
    $result = $client->startBatchIngestion($data);
    echo "✅ Ingesta iniciada. Job ID: " . $result['job_id'] . "\n";
    
    // Esperar a que se complete
    $finalStatus = $client->waitForCompletion($result['job_id']);
    
    if ($finalStatus['status'] === 'COMPLETED') {
        echo "🎉 Ingesta completada exitosamente!\n";
        if (isset($finalStatus['processed_count'])) {
            echo "📊 Procesados: " . json_encode($finalStatus['processed_count']) . "\n";
        }
    } else {
        echo "❌ Ingesta falló: " . ($finalStatus['error_message'] ?? 'Error desconocido') . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
```

## Monitoreo del Proceso

### Verificación del Estado

Para verificar el estado de un trabajo de ingesta, utilice el endpoint `/api/v1/ingestion/batch/{job_id}`:

```bash
curl -X GET "https://api.rayuela.ai/api/v1/ingestion/batch/123" \
  -H "X-API-Key: tu_api_key"
```

### Estados Posibles

- **PENDING**: El trabajo está en cola esperando ser procesado
- **PROCESSING**: El trabajo está siendo procesado actualmente
- **COMPLETED**: El trabajo se completó exitosamente
- **FAILED**: El trabajo falló debido a un error
- **CANCELED**: El trabajo fue cancelado (funcionalidad futura)

### Respuesta de Estado

La respuesta incluirá información detallada sobre el progreso del trabajo:

```json
{
  "job_id": 123,
  "status": "PROCESSING",
  "created_at": "2023-06-15T14:30:00Z",
  "started_at": "2023-06-15T14:30:05Z",
  "completed_at": null,
  "error_message": null,
  "task_id": "celery-task-uuid",
  "processed_count": {
    "users": 150,
    "products": 75,
    "interactions": 1200,
    "total": 1425,
    "errors": 3
  },
  "parameters": {
    "estimated_size": 2048576,
    "total_users": 200,
    "total_products": 100,
    "total_interactions": 1500
  },
  "progress_percentage": 85.2,
  "estimated_remaining_time": 45.0,
  "success_rate": 99.8,
  "error_count": 3,
  "error_details": {
    "users": ["Usuario con external_id duplicado: 'user_duplicate'"],
    "products": ["Producto con precio inválido en línea 45"],
    "interactions": ["Referencia a usuario inexistente: user_id 999"]
  }
}
```

### Interpretación de Errores

Si ocurre un error durante el procesamiento, el campo `error_message` contendrá información sobre el problema. Los errores se categorizan en:

#### Errores de Validación de Datos
- **Campos faltantes**: Campos requeridos no proporcionados
- **Formato incorrecto**: Tipos de datos incorrectos o valores fuera de rango
- **Duplicados**: Entidades con identificadores duplicados

#### Errores de Límites
- **Límite de usuarios excedido**: Superación del límite de usuarios de su plan
- **Límite de productos excedido**: Superación del límite de productos de su plan
- **Límite de almacenamiento excedido**: Superación del límite de almacenamiento de su plan

#### Errores de Referencia
- **Usuario inexistente**: Interacción referencia un user_id que no existe
- **Producto inexistente**: Interacción referencia un product_id que no existe

## Manejo de Errores Comunes

### Error 422: Datos de Validación Incorrectos

**Problema**: Los datos enviados no cumplen con el esquema requerido.

**Ejemplo de error**:
```json
{
  "detail": [
    {
      "type": "missing",
      "loc": ["body", "products", 0, "name"],
      "msg": "Field required",
      "input": {"price": 99.99, "category": "electronics"}
    }
  ]
}
```

**Solución**:
1. Verifique que todos los campos requeridos estén presentes
2. Confirme que los tipos de datos sean correctos
3. Valide que los valores estén dentro de los rangos permitidos

**Ejemplo corregido**:
```json
{
  "products": [
    {
      "name": "Producto Ejemplo",  // ✅ Campo requerido agregado
      "price": 99.99,
      "category": "electronics"
    }
  ]
}
```

### Error 429: Límites Excedidos

**Problema**: Se han excedido los límites de su plan de suscripción.

**Ejemplos de errores**:
```json
{
  "detail": "Users limit exceeded for your subscription plan"
}
```

```json
{
  "detail": "Storage limit exceeded for your subscription plan: Current usage: 95MB, Limit: 100MB, Additional: 10MB"
}
```

**Soluciones**:
1. **Reducir el tamaño del lote**: Divida sus datos en lotes más pequeños
2. **Actualizar plan**: Considere actualizar a un plan superior
3. **Limpiar datos antiguos**: Elimine datos no utilizados para liberar espacio

### Error 404: Trabajo No Encontrado

**Problema**: El job_id especificado no existe o no pertenece a su cuenta.

**Ejemplo de error**:
```json
{
  "detail": "Batch ingestion job 999 not found"
}
```

**Soluciones**:
1. Verifique que el job_id sea correcto
2. Confirme que está usando la API key correcta
3. El trabajo puede haber sido eliminado automáticamente después de un tiempo

### Error 500: Error Interno del Servidor

**Problema**: Error inesperado en el servidor.

**Soluciones**:
1. Reintente la operación después de unos minutos
2. Verifique que sus datos no contengan caracteres especiales problemáticos
3. Contacte al soporte técnico si el problema persiste

### Errores de Procesamiento Parcial

**Problema**: Algunos registros se procesaron correctamente, otros fallaron.

**Ejemplo de respuesta**:
```json
{
  "status": "COMPLETED",
  "processed_count": {
    "users": 95,
    "products": 100,
    "interactions": 450,
    "total": 645,
    "errors": 5
  },
  "success_rate": 99.2,
  "error_details": {
    "users": ["Usuario duplicado: external_id 'user_001'"],
    "interactions": [
      "user_id 999 no existe",
      "product_id 888 no existe",
      "Valor de rating inválido: 6.5 (debe ser 1.0-5.0)"
    ]
  }
}
```

**Soluciones**:
1. Revise los `error_details` para identificar problemas específicos
2. Corrija los datos problemáticos
3. Reenvíe solo los registros que fallaron

## Mejores Prácticas

### 1. Preparación de Datos

- **Validación previa**: Valide sus datos antes de enviarlos
- **Codificación**: Use UTF-8 para caracteres especiales
- **Identificadores únicos**: Asegúrese de que los external_id sean únicos
- **Datos limpios**: Elimine registros duplicados o incompletos

### 2. Tamaño de Lotes

- **Lotes moderados**: Envíe lotes de 1,000-10,000 entidades para un procesamiento eficiente
- **Memoria disponible**: Considere la memoria disponible en su sistema
- **Tiempo de procesamiento**: Lotes más grandes tardan más en procesarse

### 3. Orden de Entidades

- **Dependencias**: Envíe usuarios y productos antes que las interacciones
- **Referencias**: Asegúrese de que las interacciones referencien entidades existentes
- **Consistencia**: Mantenga la consistencia referencial en sus datos

### 4. Monitoreo Activo

- **Verificación regular**: Consulte el estado cada 5-10 segundos durante el procesamiento
- **Timeouts**: Implemente timeouts apropiados para evitar esperas indefinidas
- **Logs**: Mantenga logs de sus operaciones de ingesta

### 5. Manejo de Errores

- **Reintentos**: Implemente reintentos con retroceso exponencial para errores transitorios
- **Análisis de errores**: Analice los error_details para mejorar la calidad de datos
- **Recuperación**: Tenga un plan para recuperarse de fallos parciales

### 6. Seguridad

- **API Keys**: Mantenga sus API keys seguras y no las incluya en código público
- **HTTPS**: Siempre use HTTPS para las comunicaciones
- **Datos sensibles**: No incluya información sensible en descripciones o metadatos

### 7. Rendimiento

- **Compresión**: Use compresión gzip para payloads grandes
- **Paralelización**: Para múltiples lotes, procéselos en paralelo respetando los límites de rate limiting
- **Cache**: Cache los resultados de validación para evitar consultas repetidas

## Preguntas Frecuentes

### Datos y Formato

**P: ¿Puedo enviar solo un tipo de entidad (por ejemplo, solo interacciones)?**
R: Sí, puede enviar cualquier combinación de entidades en una solicitud. Todos los campos son opcionales en el nivel superior.

**P: ¿Qué sucede si envío un usuario o producto que ya existe?**
R: Si la entidad ya existe (basado en external_id para usuarios), se actualizará con los nuevos datos proporcionados.

**P: ¿Puedo usar caracteres especiales en los nombres de productos?**
R: Sí, pero asegúrese de usar codificación UTF-8 y evite caracteres de control o caracteres que puedan causar problemas de parsing.

**P: ¿Cómo manejo productos con precios variables (ofertas, descuentos)?**
R: Envíe el precio actual del producto. Para histórico de precios, considere usar el campo `description` o metadatos adicionales.

### Límites y Rendimiento

**P: ¿Hay límites en la cantidad de datos que puedo enviar?**
R: Sí, los límites dependen de su plan de suscripción:
- **Free**: 1K usuarios, 1K productos, 10MB almacenamiento
- **Starter**: 10K usuarios, 10K productos, 100MB almacenamiento  
- **Pro**: 100K usuarios, 100K productos, 1GB almacenamiento
- **Enterprise**: Límites personalizados

**P: ¿Cuánto tiempo tarda en procesarse un trabajo de ingesta?**
R: El tiempo varía según el volumen:
- 1K registros: 10-30 segundos
- 10K registros: 1-3 minutos
- 100K registros: 5-15 minutos

**P: ¿Puedo enviar múltiples trabajos de ingesta simultáneamente?**
R: Sí, pero cada trabajo se procesará secuencialmente. Se recomienda esperar a que termine uno antes de enviar el siguiente para mejor rendimiento.

### Monitoreo y Errores

**P: ¿Puedo cancelar un trabajo de ingesta en progreso?**
R: Actualmente no es posible cancelar trabajos de ingesta una vez iniciados. Esta funcionalidad está planificada para futuras versiones.

**P: ¿Qué pasa si mi trabajo falla parcialmente?**
R: Los registros procesados exitosamente se mantendrán en el sistema. Puede reenviar solo los registros que fallaron después de corregir los errores.

**P: ¿Por cuánto tiempo se mantiene el historial de trabajos?**
R: Los trabajos se mantienen por 30 días. Después de este período, solo se conserva información básica (ID, estado final, fechas).

### Integración y Desarrollo

**P: ¿Hay SDKs disponibles para diferentes lenguajes?**
R: Actualmente proporcionamos ejemplos de código para Python, Node.js, PHP y cURL. SDKs oficiales están en desarrollo.

**P: ¿Puedo usar webhooks para recibir notificaciones cuando termine un trabajo?**
R: Los webhooks están planificados para futuras versiones. Actualmente debe usar polling para verificar el estado.

**P: ¿Cómo integro esto con mi sistema ETL existente?**
R: Puede usar los ejemplos de código proporcionados como base. Muchos sistemas ETL pueden ejecutar scripts Python o hacer llamadas HTTP directamente.

### Troubleshooting

**P: ¿Por qué recibo errores de "usuario inexistente" en las interacciones?**
R: Las interacciones usan `user_id` y `product_id` numéricos, no external_id. Asegúrese de usar los IDs correctos que se asignan después de crear usuarios y productos.

**P: ¿Cómo obtengo los IDs numéricos después de crear usuarios y productos?**
R: Actualmente debe consultar la API después de la ingesta para obtener los IDs. Una mejora futura permitirá retornar estos IDs directamente.

**P: ¿Por qué mi trabajo está en estado PENDING por mucho tiempo?**
R: Esto puede deberse a alta carga del sistema o trabajos grandes en cola. Si persiste por más de 10 minutos, contacte al soporte.

---

Para más ayuda, consulte nuestra [documentación de API](../api/) o contacte al [soporte técnico](mailto:<EMAIL>).
