import React from "react";
import Link from "next/link";
import { generateMetadata as generateSEOMetadata } from '@/lib/seo';
import { ArrowLeft, TrendingUp, Users, Target } from "lucide-react";

export const metadata = generateSEOMetadata({
  title: 'E-commerce D2C - Casos de Uso | Rayuela',
  description: 'Aumenta tu ticket promedio con recomendaciones inteligentes. Transforma tu tienda online en una experiencia personalizada que impulsa las ventas.',
  keywords: ['e-commerce', 'recomendaciones', 'personalización', 'AOV', 'conversión']
});

export default function EcommerceUseCasePage() {
  return (
    <main className="bg-white">
      {/* Breadcrumb */}
      <section className="py-6 border-b border-gray-100">
        <div className="max-w-7xl mx-auto px-6">
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <Link href="/" className="hover:text-purple-600 transition-colors flex items-center gap-1">
              <ArrowLeft className="w-4 h-4" />
              Inicio
            </Link>
            <span>/</span>
            <span className="text-gray-900 font-medium">E-commerce</span>
          </div>
        </div>
      </section>

      {/* Hero Section */}
      <section className="py-14 md:py-16">
        <div className="max-w-7xl mx-auto px-6">
          <div className="text-center mb-12">
            <div className="inline-flex items-center gap-2 bg-purple-50 text-purple-700 px-4 py-2 rounded-full text-sm font-medium mb-6">
              <span>🛍️</span>{" "}
              Caso de Uso: E-commerce
            </div>
            <h1 className="text-4xl md:text-5xl font-bold leading-tight mb-6">
              Aumenta tu ticket promedio con recomendaciones inteligentes
            </h1>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto mb-8">
              Transforma tu tienda online en una experiencia personalizada que impulsa las ventas y fideliza clientes.
            </p>
          </div>

          {/* Historia de Éxito */}
          <div className="card max-w-5xl mx-auto">
            <div className="flex items-start gap-4">
              <div className="w-16 h-16 bg-gradient-to-r from-purple-600 to-purple-700 rounded-xl flex items-center justify-center flex-shrink-0">
                <span className="text-white text-2xl">🏆</span>
              </div>
              <div className="flex-1">
                <h3 className="text-xl font-bold text-gray-900 mb-3">
                  Caso de Éxito: ModaStyle aumentó ventas 30% en 60 días
                </h3>
                <p className="text-gray-600 mb-6 leading-relaxed">
                  <strong>ModaStyle</strong>, una tienda de moda online con 50,000 SKUs y 200,000 visitantes mensuales, 
                  implementó Rayuela en su homepage, páginas de producto y emails. En solo 60 días:
                </p>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                  <div className="bg-green-50 border border-green-200 rounded-lg p-4 text-center">
                    <div className="text-2xl font-bold text-green-600 mb-1">+30%</div>
                    <div className="text-sm text-gray-600">Ventas totales</div>
                  </div>
                  <div className="bg-purple-50 border border-purple-200 rounded-lg p-4 text-center">
                    <div className="text-2xl font-bold text-purple-600 mb-1">+45€</div>
                    <div className="text-sm text-gray-600">AOV promedio</div>
                  </div>
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 text-center">
                    <div className="text-2xl font-bold text-blue-600 mb-1">+28%</div>
                    <div className="text-sm text-gray-600">Repeat purchases</div>
                  </div>
                </div>
                <blockquote className="pl-4 border-l-4 border-purple-200 italic text-gray-600 bg-gray-50 p-4 rounded-r-lg">
                  "Implementamos Rayuela en 2 días. El primer mes ya vimos el ROI completo. 
                  Nuestros clientes ahora descubren productos que ni sabían que teníamos."
                  <footer className="mt-2 text-sm not-italic">
                    — <strong className="text-gray-900">María González</strong>, CTO de ModaStyle
                  </footer>
                </blockquote>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Key Metrics */}
      <section className="section-gradient-strong">
        <div className="max-w-7xl mx-auto px-6">
          <div className="grid gap-6 md:grid-cols-3">
            <div className="card text-center">
              <TrendingUp className="w-12 h-12 text-green-600 mx-auto mb-4" />
              <div className="text-3xl font-bold text-green-600 mb-2">+15%</div>
              <p className="text-gray-600">Aumento en AOV (Average Order Value)</p>
            </div>
            <div className="card text-center">
              <Target className="w-12 h-12 text-purple-600 mx-auto mb-4" />
              <div className="text-3xl font-bold text-purple-600 mb-2">+3pp</div>
              <p className="text-gray-600">Mejora en tasa de conversión</p>
            </div>
            <div className="card text-center">
              <Users className="w-12 h-12 text-blue-600 mx-auto mb-4" />
              <div className="text-3xl font-bold text-blue-600 mb-2">+28%</div>
              <p className="text-gray-600">Incremento en repeat purchases</p>
            </div>
          </div>
        </div>
      </section>

      {/* Problem & Solution */}
      <section className="py-14 md:py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-6">
          <div className="grid gap-8 md:grid-cols-2">
            <div className="card border-red-200 bg-red-50">
              <h3 className="text-xl font-bold text-red-700 mb-4">El Problema</h3>
              <ul className="space-y-3 text-gray-600">
                <li>• <strong>Productos genéricos:</strong> Todos los usuarios ven las mismas recomendaciones</li>
                <li>• <strong>Oportunidades perdidas:</strong> 70% de los visitantes se van sin comprar</li>
                <li>• <strong>Cross-selling limitado:</strong> Difícil identificar productos complementarios</li>
                <li>• <strong>Competencia feroz:</strong> Clientes comparan precios en múltiples tiendas</li>
                <li>• <strong>Costos de adquisición altos:</strong> CAC creciente en canales digitales</li>
              </ul>
            </div>

            <div className="card border-green-200 bg-green-50">
              <h3 className="text-xl font-bold text-green-700 mb-4">La Solución Rayuela</h3>
              <ul className="space-y-3 text-gray-600">
                <li>• <strong>Personalización 1:1:</strong> Cada usuario ve productos relevantes para él</li>
                <li>• <strong>Recomendaciones contextuales:</strong> Basadas en comportamiento y preferencias</li>
                <li>• <strong>Cross-selling inteligente:</strong> Productos complementarios automáticos</li>
                <li>• <strong>Experiencia diferenciada:</strong> Ventaja competitiva sostenible</li>
                <li>• <strong>Mayor LTV:</strong> Clientes más satisfechos compran más veces</li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Final */}
      <section className="py-14 md:py-16 section-gradient-strong">
        <div className="max-w-4xl mx-auto px-6 text-center">
          <h2 className="text-3xl font-bold mb-4">
            ¿Listo para aumentar tu AOV como ModaStyle?
          </h2>
          <p className="text-lg text-gray-600 mb-8 max-w-2xl mx-auto">
            Únete a cientos de tiendas online que ya están aumentando sus ventas 
            con recomendaciones inteligentes de Rayuela.
          </p>
          <div className="flex flex-wrap gap-4 justify-center">
            <Link href="/register?utm_source=ecommerce-final" className="btn-primary">
              Empezar gratis ahora
            </Link>
            <Link href="/contact-sales?utm_source=ecommerce-final&industry=ecommerce" className="btn-secondary">
              Hablar con experto en e-commerce
            </Link>
          </div>
          <p className="text-sm text-gray-500 mt-6">
            Sin compromiso • Integración en 48 horas • ROI visible en 30 días
          </p>
        </div>
      </section>
    </main>
  );
}
