import { defineConfig } from 'orval';
import path from 'path';

const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8001';

// Determinar si usar URL directa o archivo estático
const isProduction = process.env.NODE_ENV === 'production';
const isStaticBuild = process.env.BUILD_STATIC === 'true';
const forceStaticFile = process.env.ORVAL_USE_STATIC === 'true';

// SECURITY: Determinar qué versión del OpenAPI usar
const usePrivateAPI = process.env.ORVAL_USE_PRIVATE_API === 'true';
const useDirectUrl = !isProduction && !isStaticBuild && !forceStaticFile;

// SECURITY: Usar versión pública por defecto para proteger IP
const apiEndpoint = usePrivateAPI ? '/api/openapi.json' : '/api/public-openapi.json';

const inputConfig = useDirectUrl
  ? {
      target: `${backendUrl}${apiEndpoint}`,
    }
  : {
      target: path.resolve(process.cwd(), 'src/lib/openapi/openapi.json'),
    };

console.log(`🔧 Orval input configuration: ${useDirectUrl ? 'Direct URL' : 'Static file'}`);
console.log(`🛡️ API Security: Using ${usePrivateAPI ? 'PRIVATE' : 'PUBLIC'} OpenAPI specification`);
if (useDirectUrl) {
  console.log(`📡 Backend URL: ${backendUrl}${apiEndpoint}`);
} else {
  console.log(`📁 Static file: ${inputConfig.target}`);
}

export default defineConfig({
  rayuelaApi: {
    output: {
      mode: 'single',
      target: 'src/lib/generated/rayuelaAPI.ts',
      client: 'axios',
      prettier: true,
      mock: false, // Desactivar generación de mocks
      override: {
        operations: {
          // Personalizar nombres de operaciones si es necesario
        },
      },
    },
    input: inputConfig,
    hooks: {
      afterAllFilesWrite: 'prettier --write src/lib/generated',
    },
  },
});
