# Python
__pycache__/
*.pyc
*.pyo
*.pyd
*.so
.Python
*.egg-info/
dist/
build/

# Virtual environments
.venv/
venv/
env/

# Environment variables
.env
.env.*
.env.local
.env.production
.env.development
.env.staging

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# IDE/Editor
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Testing
.pytest_cache/
.coverage
htmlcov/
.tox/
.cache
coverage.xml
*.cover
.hypothesis/

# Logs
*.log
logs/

# Database
*.db
*.sqlite3

# Cloud Build local testing
.docker/

# Temporary files
*.tmp
*.temp
*.bak
