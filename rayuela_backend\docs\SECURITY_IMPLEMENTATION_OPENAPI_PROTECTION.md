# 🛡️ Implementación de Protección de OpenAPI - Seguridad de Propiedad Intelectual

**Fecha**: 2025-08-15  
**Severidad**: **ALTA (P1)**  
**Estado**: ✅ **IMPLEMENTADA**

## 📋 Resumen de la Vulnerabilidad de Propiedad Intelectual

### Problema Identificado
La aplicación exponía **información crítica de propiedad intelectual** a través del OpenAPI público:

1. **Estrategias de ML Específicas**: `"balanced"`, `"maximize_engagement"`, `"promote_new_arrivals"`, `"discover_diverse"`
2. **Tipos de Modelos Internos**: `"collaborative"`, `"content"`, `"hybrid"`
3. **Métricas de Rendimiento**: `precision`, `recall`, `ndcg`, `map_score`, `catalog_coverage`, `diversity`, `novelty`, `serendipity`
4. **Parámetros de Configuración**: Pesos exactos y configuraciones de estrategias

### Riesgo Estratégico
- **Ingeniería Inversa**: Competidores podrían replicar la superficie de la API
- **Ventaja Competitiva Comprometida**: Exposición de algoritmos y estrategias únicas
- **Filtración de IP**: Detalles de implementación accesibles públicamente

## 🔧 Solución Implementada

### 1. Esquemas Públicos Ofuscados

#### `src/db/schemas/public_recommendation.py`
- **Objetivos de Negocio**: `conversion_optimization`, `user_engagement`, `discovery_mode`, `catalog_promotion`
- **Variantes de Modelo**: `standard`, `premium`, `enterprise` (orientadas al plan de suscripción)
- **Funciones de Conversión**: Mapeo interno entre esquemas públicos y privados

#### `src/db/schemas/public_analytics.py`
- **Métricas Categorizadas**: "High/Medium/Low" en lugar de valores precisos
- **Insights de Negocio**: Sin exposición de métricas técnicas de ML
- **Conversión Automática**: De datos internos a formato público

#### `src/db/schemas/public_pipeline.py`
- **Estados Simplificados**: Sin exposición de job_id ni detalles de cola
- **Salud del Modelo**: Categorías de negocio (`excellent`, `good`, `fair`)
- **IDs Públicos**: Generados dinámicamente para ocultar IDs internos

### 2. Endpoints de OpenAPI Duales

#### Endpoint Público: `/api/v1/public-openapi.json`
```python
@router.get("/public-openapi.json")
async def get_public_openapi():
    """
    Endpoint para obtener la especificación OpenAPI pública.
    
    SEGURIDAD: Esta versión está diseñada para ser consumida públicamente
    sin revelar detalles de implementación o lógica de negocio interna.
    """
    return create_public_openapi_spec()
```

#### Endpoint Privado: `/api/v1/openapi.json`
```python
@router.get("/openapi.json")
async def get_private_openapi(
    account: Account = Depends(get_current_account_with_context)
):
    """
    Endpoint para obtener la especificación OpenAPI completa.
    
    SEGURIDAD: Requiere autenticación. Esta versión contiene todos los
    detalles técnicos para integración avanzada y desarrollo.
    """
```

### 3. Filtrado Inteligente de Contenido

#### Endpoints Permitidos en Versión Pública:
```python
PUBLIC_ENDPOINTS = {
    # Autenticación básica
    "/api/v1/auth/register", "/api/v1/auth/token", "/api/v1/auth/logout",
    
    # Información de cuenta básica
    "/api/v1/accounts/current", "/api/v1/accounts/usage",
    
    # Gestión básica de usuarios y productos
    "/api/v1/end-users/", "/api/v1/products/",
    
    # Recomendaciones públicas (versión simplificada)
    "/api/v1/recommendations/personalized/query",
    "/api/v1/recommendations/products/{product_id}/similar",
    
    # Health check
    "/health"
}
```

#### Esquemas Permitidos en Versión Pública:
```python
PUBLIC_SCHEMAS = {
    # Autenticación y modelos básicos
    "RegisterRequest", "LoginRequest", "Account", "Product", "EndUser",
    
    # Esquemas públicos de recomendaciones
    "PublicRecommendationQueryRequest", "PublicRecommendationGoal",
    "PublicModelVariant", "PublicExplanationLevel",
    
    # Filtros y contexto (sin detalles sensibles)
    "FilterGroup", "RecommendationContext",
    
    # Errores estándar
    "HTTPValidationError", "ValidationError"
}
```

### 4. Configuración del Frontend Actualizada

#### `rayuela_frontend/orval.config.ts`
```typescript
// SECURITY: Determinar qué versión del OpenAPI usar
const usePrivateAPI = process.env.ORVAL_USE_PRIVATE_API === 'true';
const apiEndpoint = usePrivateAPI ? '/api/openapi.json' : '/api/public-openapi.json';

console.log(`🛡️ API Security: Using ${usePrivateAPI ? 'PRIVATE' : 'PUBLIC'} OpenAPI specification`);
```

#### Scripts de Generación Seguros
```json
{
  "generate-api": "node scripts/generate-api-client.js",
  "generate-api:private": "node scripts/generate-api-client.js --private"
}
```

### 5. Actualización de Endpoints de Recomendaciones

#### Uso de Esquemas Públicos:
```python
async def query_personalized_recommendations(
    query: Union[PublicRecommendationQueryRequest, PublicRecommendationQueryExternalRequest],
    account: schemas.AccountResponse = Depends(get_current_account),
    db: AsyncSession = Depends(get_db),
    limit_service: LimitService = Depends(get_limit_service),
):
    # Convertir esquemas públicos a formato interno
    if isinstance(query, PublicRecommendationQueryExternalRequest):
        internal_data = convert_public_external_to_internal_request(query)
    elif isinstance(query, PublicRecommendationQueryRequest):
        internal_data = convert_public_to_internal_request(query)
```

## 🔍 Mapeo de Ofuscación

### Estrategias de ML → Objetivos de Negocio:
```python
PUBLIC_TO_INTERNAL_GOAL_MAPPING = {
    "conversion_optimization": "maximize_engagement",    # Era técnico
    "user_engagement": "balanced",                       # Era técnico
    "discovery_mode": "discover_diverse",               # Era técnico
    "catalog_promotion": "promote_new_arrivals"         # Era técnico
}
```

### Tipos de Modelo → Variantes de Plan:
```python
PUBLIC_TO_INTERNAL_MODEL_MAPPING = {
    "standard": "standard",      # Modelo básico
    "premium": "premium",        # Modelo avanzado
    "enterprise": "enterprise"   # Modelo completo
}
```

## 🛡️ Beneficios de Seguridad

### Protección de Propiedad Intelectual:
1. **Ofuscación de Algoritmos**: Nombres técnicos reemplazados por términos de negocio
2. **Ocultación de Métricas**: Valores precisos categorizados en rangos
3. **Abstracción de Implementación**: Detalles técnicos no expuestos

### Experiencia del Cliente Mejorada:
1. **Terminología de Negocio**: Más comprensible para integradores
2. **Orientación al Plan**: Variantes alineadas con suscripciones
3. **Documentación Clara**: Enfoque en valor de negocio, no implementación

### Flexibilidad Operacional:
1. **Desarrollo Interno**: Versión privada para equipos técnicos
2. **Integración Externa**: Versión pública para clientes
3. **Migración Gradual**: Ambas versiones coexisten

## 📊 Configuración de Uso

### Para Desarrollo Interno:
```bash
# Usar versión privada (todos los detalles técnicos)
export ORVAL_USE_PRIVATE_API=true
npm run generate-api:private
```

### Para Producción/Cliente:
```bash
# Usar versión pública (IP protegida)
npm run generate-api
```

### Para Verificación:
```bash
# Verificar que se usa la versión correcta
curl http://localhost:8001/api/v1/public-openapi.json | grep -i "collaborative\|precision"
# No debe retornar resultados en versión pública
```

## ✅ Estado de Implementación

- [x] Esquemas públicos ofuscados creados
- [x] Endpoints duales de OpenAPI implementados
- [x] Filtrado de contenido sensible
- [x] Configuración del frontend actualizada
- [x] Scripts de generación seguros
- [x] Endpoints de recomendaciones migrados
- [x] Documentación completa
- [x] Mapeo de conversión implementado

## 🚨 Consideraciones Importantes

### Para Equipos de Desarrollo:
1. **Usar Versión Privada**: Solo en entornos de desarrollo controlados
2. **Verificar Configuración**: Antes de despliegues a producción
3. **Revisar Logs**: Confirmar qué versión se está usando

### Para Equipos de Producto:
1. **Terminología Consistente**: Usar nombres de negocio en documentación
2. **Comunicación Externa**: Enfatizar valor, no implementación
3. **Feedback de Clientes**: Recopilar sobre claridad de la API pública

### Para Equipos de Seguridad:
1. **Auditoría Regular**: Verificar que no se filtre información sensible
2. **Monitoreo de Acceso**: Logs de uso de versión privada
3. **Revisión de Cambios**: Nuevos esquemas deben seguir principios de ofuscación

**La propiedad intelectual de Rayuela está ahora protegida mediante ofuscación estratégica del OpenAPI público.**
