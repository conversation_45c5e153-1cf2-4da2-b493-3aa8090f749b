"""
Servicio para la gestión de interacciones.
"""

from typing import Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import func, update
from redis.asyncio import Redis
from fastapi import HTTPException, status
from datetime import datetime

from src.db import schemas
from src.db.models.end_user import EndUser
from src.db.models.product import Product
from src.db.models.interaction import Interaction
from src.core.exceptions import (
    ResourceNotFoundError,
    RateLimitExceededError,
    ProductNotFoundException,
    LimitExceededError,
)
from src.utils.base_logger import log_error, log_info
from src.services import LimitService
from src.services.cache_service import get_cache_service


class InteractionService:
    """
    Servicio para la gestión de interacciones entre usuarios y productos.

    Este servicio maneja la validación de usuarios y productos, límites de tasa,
    y la creación de interacciones. Utiliza el CacheService centralizado para
    invalidar cachés de forma explícita después de operaciones relevantes.
    """

    def __init__(
        self, db: AsyncSession, account_id, redis: Redis, limit_service: LimitService
    ):
        """
        Inicializa el servicio de interacciones.

        Args:
            db: Sesión de base de datos
            account_id: ID de la cuenta (puede ser un entero o un objeto Column)
            redis: Cliente Redis para límites de tasa
            limit_service: Servicio de límites
        """
        self.db = db
        # Convertir account_id a entero si es necesario
        self.account_id = account_id.id if hasattr(account_id, "id") else account_id
        self.redis = redis
        self.limit_service = limit_service

    async def verify_user_exists(self, user_id: int) -> EndUser:
        """
        Verifica que un usuario exista y pertenezca a la cuenta.

        Args:
            user_id: ID del usuario

        Returns:
            Objeto EndUser si existe

        Raises:
            ResourceNotFoundError: Si el usuario no existe o no pertenece a la cuenta
        """
        end_user_query = select(EndUser).where(
            EndUser.user_id == user_id, EndUser.account_id == self.account_id
        )
        end_user_result = await self.db.execute(end_user_query)
        end_user = end_user_result.scalars().first()

        if not end_user:
            raise ResourceNotFoundError("EndUser", user_id)

        return end_user

    async def verify_product_exists(self, product_id: int) -> Product:
        """
        Verifica que un producto exista y pertenezca a la cuenta.

        Args:
            product_id: ID del producto

        Returns:
            Objeto Product si existe

        Raises:
            ProductNotFoundException: Si el producto no existe o no pertenece a la cuenta
        """
        product_query = select(Product).where(
            Product.product_id == product_id, Product.account_id == self.account_id
        )
        product_result = await self.db.execute(product_query)
        product = product_result.scalars().first()

        if not product:
            raise ProductNotFoundException(product_id)

        return product

    async def check_rate_limit(self, user_id: int) -> None:
        """
        Verifica si el usuario ha excedido el límite de tasa para interacciones.

        Args:
            user_id: ID del usuario

        Raises:
            RateLimitExceededError: Si se ha excedido el límite de tasa
        """
        rate_key = f"interaction_rate:{self.account_id}:{user_id}"
        if await self.redis.exists(rate_key):
            raise RateLimitExceededError(
                "Too many interactions. Please try again later."
            )

    async def set_rate_limit(self, user_id: int, cooldown_seconds: int = 60) -> None:
        """
        Establece un límite de tasa para un usuario.

        Args:
            user_id: ID del usuario
            cooldown_seconds: Tiempo de espera en segundos
        """
        rate_key = f"interaction_rate:{self.account_id}:{user_id}"
        await self.redis.setex(rate_key, cooldown_seconds, "1")

    async def create_interaction(
        self, interaction_data: schemas.InteractionCreate
    ) -> Interaction:
        """
        Crea una nueva interacción entre un usuario y un producto.
        Supports both internal IDs (user_id, product_id) and external IDs (external_user_id, external_product_id).

        Args:
            interaction_data: Datos de la interacción

        Returns:
            Objeto Interaction creado

        Raises:
            HTTPException: Si ocurre un error durante la creación
            ResourceNotFoundError: Si el usuario no existe
            ProductNotFoundException: Si el producto no existe
            RateLimitExceededError: Si se ha excedido el límite de tasa
        """
        try:
            # Validar límites de interacciones (fuera de la transacción)
            await self.limit_service.validate_interaction_limit()

            # Determine if we're using internal or external IDs and resolve them
            if interaction_data.user_id is not None and interaction_data.product_id is not None:
                # Using internal IDs
                user_id = interaction_data.user_id
                product_id = interaction_data.product_id

                # Verificar usuario (fuera de la transacción)
                end_user = await self.verify_user_exists(user_id)

                # Verificar producto (fuera de la transacción)
                product = await self.verify_product_exists(product_id)

            elif interaction_data.external_user_id is not None and interaction_data.external_product_id is not None:
                # Using external IDs - resolve to internal IDs
                end_user = await self.verify_user_exists_external(interaction_data.external_user_id)
                product = await self.verify_product_exists_external(interaction_data.external_product_id)

            else:
                # This should not happen due to schema validation, but just in case
                raise ValueError("Either internal IDs or external IDs must be provided")

            # Verificar límite de tasa (fuera de la transacción)
            await self.check_rate_limit(end_user.user_id)

            # Iniciar transacción para operaciones de escritura
            async with self.db.begin():
                # Obtener timestamp actual para consistencia
                current_time = datetime.utcnow()

                # Crear interacción
                interaction = Interaction(
                    account_id=self.account_id,
                    user_id=end_user.user_id,
                    product_id=product.product_id,
                    interaction_type=interaction_data.interaction_type,
                    value=interaction_data.value,
                    timestamp=current_time,
                    recommendation_metadata=interaction_data.recommendation_metadata,
                )

                self.db.add(interaction)
                # La transacción se confirma automáticamente al salir del bloque

            # Establecer límite de tasa (fuera de la transacción)
            await self.set_rate_limit(end_user.user_id, 5)

            # Invalidar caché de recomendaciones usando el servicio centralizado
            try:
                cache_service = await get_cache_service()
                await cache_service.invalidate_after_interaction(
                    account_id=self.account_id,
                    user_id=end_user.user_id
                )
            except Exception as cache_error:
                # No fallar la operación principal por errores de caché
                log_error(f"Error invalidando caché después de interacción: {str(cache_error)}")

            log_info(
                f"Interacción creada: user_id={end_user.user_id}, "
                f"product_id={product.product_id}, account_id={self.account_id}"
            )

            return interaction

        except (
            ResourceNotFoundError,
            ProductNotFoundException,
            RateLimitExceededError,
            LimitExceededError,
        ):
            # Re-lanzar excepciones conocidas sin modificar
            raise
        except Exception as e:
            log_error(f"Error inesperado creando interacción: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error interno del servidor",
            )

    async def get_interactions(self, skip: int = 0, limit: int = 10) -> Dict[str, Any]:
        """
        Obtiene las interacciones de la cuenta con paginación.

        Args:
            skip: Número de elementos a saltar
            limit: Número máximo de elementos a devolver

        Returns:
            Diccionario con las interacciones y metadatos de paginación
        """
        try:
            # Usar índice compuesto account_id, timestamp
            interactions_query = (
                select(Interaction)
                .where(Interaction.account_id == self.account_id)
                .order_by(Interaction.timestamp.desc())
                .with_hint(Interaction, "USE INDEX (idx_interaction_account_timestamp)")
                .offset(skip)
                .limit(limit)
            )

            result = await self.db.execute(interactions_query)
            interactions = result.scalars().all()

            # Obtener el total de interacciones
            total_query = (
                select(func.count())
                .select_from(Interaction)
                .where(Interaction.account_id == self.account_id)
            )
            total_result = await self.db.execute(total_query)
            total = total_result.scalar_one()

            return {
                "items": interactions,
                "total": total,
                "page": skip // limit + 1 if limit > 0 else 1,
                "size": limit,
            }

        except Exception as e:
            log_error(f"Error getting interactions: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error getting interactions: {str(e)}",
            )

    # ------------------------------------------------------------------
    # Métodos con soporte de IDs EXTERNOS
    # ------------------------------------------------------------------

    async def verify_user_exists_external(self, external_user_id: str) -> EndUser:
        """Obtiene y valida un EndUser por external_id."""
        query = select(EndUser).where(
            EndUser.external_id == external_user_id,
            EndUser.account_id == self.account_id,
        )
        result = await self.db.execute(query)
        end_user = result.scalars().first()
        if not end_user:
            raise ResourceNotFoundError("EndUser", external_user_id)
        return end_user

    async def verify_product_exists_external(self, external_product_id: str) -> Product:
        """Obtiene y valida un Product por external_id."""
        query = select(Product).where(
            Product.external_id == external_product_id,
            Product.account_id == self.account_id,
        )
        result = await self.db.execute(query)
        product = result.scalars().first()
        if not product:
            raise ProductNotFoundException(external_product_id)
        return product

    async def create_interaction_external(
        self, interaction_data: schemas.InteractionExternalCreate
    ) -> Interaction:
        """Crea una interacción usando external_user_id y external_product_id."""
        # Obtener entidades internas
        end_user = await self.verify_user_exists_external(interaction_data.external_user_id)
        product = await self.verify_product_exists_external(interaction_data.external_product_id)

        # Construir objeto interno equivalente
        internal_data = schemas.InteractionCreate(
            user_id=end_user.user_id,
            product_id=product.product_id,
            interaction_type=interaction_data.interaction_type,
            value=interaction_data.value,
            recommendation_metadata=interaction_data.recommendation_metadata,
        )

        return await self.create_interaction(internal_data)
