import React from "react";
import Link from "next/link";
import { generateMetadata as generateSEOMetadata } from '@/lib/seo';
import { ArrowLeft, TrendingUp, Users, Target } from "lucide-react";

export const metadata = generateSEOMetadata({
  title: 'E-commerce D2C - Casos de Uso | Rayuela',
  description: 'Aumenta tu ticket promedio con recomendaciones inteligentes. Transforma tu tienda online en una experiencia personalizada que impulsa las ventas.',
  keywords: ['e-commerce', 'recomendaciones', 'personalización', 'AOV', 'conversión'],
  path: '/use-cases/ecommerce'
});

export default function EcommerceUseCasePage() {
  return (
    <main className="bg-white">
      {/* Breadcrumb */}
      <section className="py-6 border-b border-gray-100">
        <div className="max-w-7xl mx-auto px-6">
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <Link href="/" className="hover:text-purple-600 transition-colors flex items-center gap-1">
              <ArrowLeft className="w-4 h-4" />
              Inicio
            </Link>
            <span>/</span>
            <span className="text-gray-900 font-medium">E-commerce</span>
          </div>
        </div>
      </section>

      {/* Hero Section */}
      <section className="py-14 md:py-16">
        <div className="max-w-7xl mx-auto px-6">
          <div className="text-center mb-12">
            <div className="inline-flex items-center gap-2 bg-purple-50 text-purple-700 px-4 py-2 rounded-full text-sm font-medium mb-6">
              <span>🛍️</span>{" "}
              Caso de Uso: E-commerce
            </div>
            <h1 className="text-4xl md:text-5xl font-bold leading-tight mb-6">
              Aumenta tu ticket promedio con recomendaciones inteligentes
            </h1>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto mb-8">
              Transforma tu tienda online en una experiencia personalizada que impulsa las ventas y fideliza clientes.
            </p>
          </div>

          {/* Historia de Éxito */}
          <div className="card max-w-5xl mx-auto">
            <div className="flex items-start gap-4">
              <div className="w-16 h-16 bg-gradient-to-r from-purple-600 to-purple-700 rounded-xl flex items-center justify-center flex-shrink-0">
                <span className="text-white text-2xl">🏆</span>
              </div>
              <div className="flex-1">
                <h3 className="text-xl font-bold text-gray-900 mb-3">
                  Caso de Éxito: ModaStyle aumentó ventas 30% en 60 días
                </h3>
                <p className="text-gray-600 mb-6 leading-relaxed">
                  <strong>ModaStyle</strong>, una tienda de moda online con 50,000 SKUs y 200,000 visitantes mensuales, 
                  implementó Rayuela en su homepage, páginas de producto y emails. En solo 60 días:
                </p>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                  <div className="bg-green-50 border border-green-200 rounded-lg p-4 text-center">
                    <div className="text-2xl font-bold text-green-600 mb-1">+30%</div>
                    <div className="text-sm text-gray-600">Ventas totales</div>
                  </div>
                  <div className="bg-purple-50 border border-purple-200 rounded-lg p-4 text-center">
                    <div className="text-2xl font-bold text-purple-600 mb-1">+45€</div>
                    <div className="text-sm text-gray-600">AOV promedio</div>
                  </div>
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 text-center">
                    <div className="text-2xl font-bold text-blue-600 mb-1">+28%</div>
                    <div className="text-sm text-gray-600">Repeat purchases</div>
                  </div>
                </div>
                <blockquote className="pl-4 border-l-4 border-purple-200 italic text-gray-600 bg-gray-50 p-4 rounded-r-lg">
                  "Implementamos Rayuela en 2 días. El primer mes ya vimos el ROI completo. 
                  Nuestros clientes ahora descubren productos que ni sabían que teníamos."
                  <footer className="mt-2 text-sm not-italic">
                    — <strong className="text-gray-900">María González</strong>, CTO de ModaStyle
                  </footer>
                </blockquote>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Key Metrics */}
      <section className="section-gradient-strong">
        <div className="max-w-7xl mx-auto px-6">
          <div className="grid gap-6 md:grid-cols-3">
            <div className="card text-center">
              <TrendingUp className="w-12 h-12 text-green-600 mx-auto mb-4" />
              <div className="text-3xl font-bold text-green-600 mb-2">+15%</div>
              <p className="text-gray-600">Aumento en AOV (Average Order Value)</p>
            </div>
            <div className="card text-center">
              <Target className="w-12 h-12 text-purple-600 mx-auto mb-4" />
              <div className="text-3xl font-bold text-purple-600 mb-2">+3pp</div>
              <p className="text-gray-600">Mejora en tasa de conversión</p>
            </div>
            <div className="card text-center">
              <Users className="w-12 h-12 text-blue-600 mx-auto mb-4" />
              <div className="text-3xl font-bold text-blue-600 mb-2">+28%</div>
              <p className="text-gray-600">Incremento en repeat purchases</p>
            </div>
          </div>
        </div>
      </section>

      {/* Problem & Solution */}
      <section className="py-14 md:py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-6">
          <div className="grid gap-8 md:grid-cols-2">
            <div className="card border-red-200 bg-red-50">
              <h3 className="text-xl font-bold text-red-700 mb-4">El Problema</h3>
              <ul className="space-y-3 text-gray-600">
                <li>• <strong>Productos genéricos:</strong> Todos los usuarios ven las mismas recomendaciones</li>
                <li>• <strong>Oportunidades perdidas:</strong> 70% de los visitantes se van sin comprar</li>
                <li>• <strong>Cross-selling limitado:</strong> Difícil identificar productos complementarios</li>
                <li>• <strong>Competencia feroz:</strong> Clientes comparan precios en múltiples tiendas</li>
                <li>• <strong>Costos de adquisición altos:</strong> CAC creciente en canales digitales</li>
              </ul>
            </div>

            <div className="card border-green-200 bg-green-50">
              <h3 className="text-xl font-bold text-green-700 mb-4">La Solución Rayuela</h3>
              <ul className="space-y-3 text-gray-600">
                <li>• <strong>Personalización 1:1:</strong> Cada usuario ve productos relevantes para él</li>
                <li>• <strong>Recomendaciones contextuales:</strong> Basadas en comportamiento y preferencias</li>
                <li>• <strong>Cross-selling inteligente:</strong> Productos complementarios automáticos</li>
                <li>• <strong>Experiencia diferenciada:</strong> Ventaja competitiva sostenible</li>
                <li>• <strong>Mayor LTV:</strong> Clientes más satisfechos compran más veces</li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Dónde colocar las recomendaciones */}
      <section className="py-14 md:py-16">
        <div className="max-w-7xl mx-auto px-6">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Dónde colocar las recomendaciones para máximo impacto</h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Puntos estratégicos que generan más conversión y AOV
            </p>
          </div>

          <div className="card">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Diagrama Visual */}
              <div className="space-y-6">
                <h4 className="font-bold text-lg mb-4">Flujo del Cliente</h4>

                <div className="space-y-4">
                  <div className="flex items-center gap-4 p-4 bg-purple-50 rounded-lg border border-purple-200">
                    <div className="w-8 h-8 bg-purple-600 text-white rounded-full flex items-center justify-center text-sm font-bold">1</div>
                    <div>
                      <div className="font-semibold text-gray-900">Homepage</div>
                      <div className="text-sm text-gray-600">"Productos para ti" personalizados</div>
                    </div>
                  </div>

                  <div className="flex items-center gap-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
                    <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold">2</div>
                    <div>
                      <div className="font-semibold text-gray-900">Página de Producto</div>
                      <div className="text-sm text-gray-600">"Frecuentemente comprados juntos"</div>
                    </div>
                  </div>

                  <div className="flex items-center gap-4 p-4 bg-green-50 rounded-lg border border-green-200">
                    <div className="w-8 h-8 bg-green-600 text-white rounded-full flex items-center justify-center text-sm font-bold">3</div>
                    <div>
                      <div className="font-semibold text-gray-900">Carrito</div>
                      <div className="text-sm text-gray-600">"Completa tu look" - cross-selling</div>
                    </div>
                  </div>

                  <div className="flex items-center gap-4 p-4 bg-orange-50 rounded-lg border border-orange-200">
                    <div className="w-8 h-8 bg-orange-600 text-white rounded-full flex items-center justify-center text-sm font-bold">4</div>
                    <div>
                      <div className="font-semibold text-gray-900">Post-Compra</div>
                      <div className="text-sm text-gray-600">Emails con "Te puede interesar"</div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Métricas por ubicación */}
              <div className="space-y-6">
                <h4 className="font-bold text-lg mb-4">Impacto por Ubicación</h4>

                <div className="space-y-4">
                  <div className="p-4 bg-gray-50 rounded-lg">
                    <div className="flex justify-between items-center mb-2">
                      <span className="font-semibold">Homepage</span>
                      <span className="text-purple-600 font-bold">+15% engagement</span>
                    </div>
                    <div className="text-sm text-gray-600">Usuarios descubren productos relevantes desde el primer momento</div>
                  </div>

                  <div className="p-4 bg-gray-50 rounded-lg">
                    <div className="flex justify-between items-center mb-2">
                      <span className="font-semibold">Página de Producto</span>
                      <span className="text-blue-600 font-bold">+25% AOV</span>
                    </div>
                    <div className="text-sm text-gray-600">Cross-selling efectivo con productos complementarios</div>
                  </div>

                  <div className="p-4 bg-gray-50 rounded-lg">
                    <div className="flex justify-between items-center mb-2">
                      <span className="font-semibold">Carrito</span>
                      <span className="text-green-600 font-bold">+35% conversión</span>
                    </div>
                    <div className="text-sm text-gray-600">Último momento para aumentar ticket promedio</div>
                  </div>

                  <div className="p-4 bg-gray-50 rounded-lg">
                    <div className="flex justify-between items-center mb-2">
                      <span className="font-semibold">Email Marketing</span>
                      <span className="text-orange-600 font-bold">+40% repeat</span>
                    </div>
                    <div className="text-sm text-gray-600">Reactivación y fidelización automática</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* ROI Calculator */}
      <section className="py-14 md:py-16 section-gradient-strong">
        <div className="max-w-7xl mx-auto px-6">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Calculadora de ROI</h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Estima el impacto financiero de implementar Rayuela en tu e-commerce
            </p>
          </div>

          <div className="card max-w-5xl mx-auto">
            <div className="grid gap-8 md:grid-cols-2">
              <div className="space-y-4">
                <h4 className="font-bold text-lg">Ejemplo: Tienda con 10,000 visitantes/mes</h4>
                <div className="space-y-3 text-sm">
                  <div className="flex justify-between p-3 bg-gray-50 rounded">
                    <span>Visitantes mensuales:</span>
                    <span className="font-mono font-semibold">10,000</span>
                  </div>
                  <div className="flex justify-between p-3 bg-gray-50 rounded">
                    <span>Conversión actual (2%):</span>
                    <span className="font-mono font-semibold">200 órdenes</span>
                  </div>
                  <div className="flex justify-between p-3 bg-gray-50 rounded">
                    <span>AOV actual:</span>
                    <span className="font-mono font-semibold">$75</span>
                  </div>
                  <div className="flex justify-between p-3 bg-gray-100 rounded border-l-4 border-gray-400">
                    <span className="font-semibold">Revenue mensual actual:</span>
                    <span className="font-mono font-bold">$15,000</span>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <h4 className="font-bold text-lg text-green-700">Con Rayuela:</h4>
                <div className="space-y-3 text-sm">
                  <div className="flex justify-between p-3 bg-green-50 rounded">
                    <span>Conversión mejorada (2.6%):</span>
                    <span className="font-mono font-semibold text-green-700">260 órdenes</span>
                  </div>
                  <div className="flex justify-between p-3 bg-green-50 rounded">
                    <span>AOV mejorado:</span>
                    <span className="font-mono font-semibold text-green-700">$86</span>
                  </div>
                  <div className="flex justify-between p-3 bg-green-50 rounded">
                    <span>Revenue mensual nuevo:</span>
                    <span className="font-mono font-semibold text-green-700">$22,360</span>
                  </div>
                  <div className="flex justify-between p-3 bg-green-100 rounded border-l-4 border-green-500">
                    <span className="font-semibold">Incremento mensual:</span>
                    <span className="font-mono font-bold text-green-700">+$7,360</span>
                  </div>
                  <div className="flex justify-between p-3 bg-green-200 rounded border-l-4 border-green-600">
                    <span className="font-bold">ROI anual:</span>
                    <span className="font-mono font-bold text-green-800 text-lg">+$88,320</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="mt-8 p-6 bg-purple-50 rounded-lg border border-purple-200">
              <div className="text-center">
                <div className="text-purple-700 font-semibold mb-2">💡 Costo de Rayuela: $99/mes</div>
                <div className="text-purple-600 text-sm">
                  ROI de <strong>7,360%</strong> en el primer año • Recuperas la inversión en <strong>4 días</strong>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Beneficios Técnicos */}
      <section className="py-14 md:py-16">
        <div className="max-w-7xl mx-auto px-6">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Beneficios Técnicos para tu Equipo</h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Implementación simple que no requiere cambios en tu infraestructura
            </p>
          </div>

          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
            <div className="card text-center">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <span className="text-green-600 text-xl">⚡</span>
              </div>
              <h4 className="font-bold mb-2">Integración en 1 día</h4>
              <p className="text-sm text-gray-600">
                API REST simple, SDKs para Python/Node.js, documentación completa
              </p>
            </div>

            <div className="card text-center">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <span className="text-blue-600 text-xl">🚀</span>
              </div>
              <h4 className="font-bold mb-2">Sin infraestructura ML</h4>
              <p className="text-sm text-gray-600">
                No necesitas data scientists ni servidores de ML. Todo en la nube
              </p>
            </div>

            <div className="card text-center">
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <span className="text-purple-600 text-xl">📈</span>
              </div>
              <h4 className="font-bold mb-2">Escalabilidad automática</h4>
              <p className="text-sm text-gray-600">
                Desde 1,000 hasta 10M+ requests/mes sin cambios en tu código
              </p>
            </div>

            <div className="card text-center">
              <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <span className="text-orange-600 text-xl">📊</span>
              </div>
              <h4 className="font-bold mb-2">Analytics en tiempo real</h4>
              <p className="text-sm text-gray-600">
                Dashboard con métricas de performance y A/B testing integrado
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonios */}
      <section className="py-14 md:py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-6">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Lo que dicen nuestros clientes de e-commerce</h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Testimonios reales de tiendas online que ya están usando Rayuela
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
            <div className="card hover:shadow-lg transition-all duration-300">
              <blockquote className="text-gray-600 mb-4 leading-relaxed italic">
                "Implementamos Rayuela en 2 días y vimos ROI inmediato. Nuestro AOV subió 15% el primer mes sin cambiar nada más."
              </blockquote>
              <div className="flex items-center gap-3 mb-3">
                <div className="w-12 h-12 bg-gradient-to-r from-purple-400 to-purple-600 rounded-full flex items-center justify-center">
                  <span className="text-white text-xl">👩‍💼</span>
                </div>
                <div>
                  <div className="font-bold text-gray-900">María González</div>
                  <div className="text-sm text-gray-600">CTO</div>
                  <div className="text-xs text-gray-500">ModaStyle</div>
                </div>
              </div>
              <div className="bg-green-50 border border-green-200 rounded-lg px-3 py-2">
                <span className="text-xs font-semibold text-green-700">+15% AOV, +30% ventas</span>
              </div>
            </div>

            <div className="card hover:shadow-lg transition-all duration-300">
              <blockquote className="text-gray-600 mb-4 leading-relaxed italic">
                "Las recomendaciones en carrito aumentaron nuestro ticket promedio €45. Es como tener un vendedor experto 24/7."
              </blockquote>
              <div className="flex items-center gap-3 mb-3">
                <div className="w-12 h-12 bg-gradient-to-r from-blue-400 to-blue-600 rounded-full flex items-center justify-center">
                  <span className="text-white text-xl">👨‍💻</span>
                </div>
                <div>
                  <div className="font-bold text-gray-900">Carlos Mendoza</div>
                  <div className="text-sm text-gray-600">E-commerce Manager</div>
                  <div className="text-xs text-gray-500">TechGadgets</div>
                </div>
              </div>
              <div className="bg-blue-50 border border-blue-200 rounded-lg px-3 py-2">
                <span className="text-xs font-semibold text-blue-700">+€45 AOV, +22% conversión</span>
              </div>
            </div>

            <div className="card hover:shadow-lg transition-all duration-300">
              <blockquote className="text-gray-600 mb-4 leading-relaxed italic">
                "Nuestros clientes ahora descubren productos que ni sabían que teníamos. Las ventas cruzadas se dispararon."
              </blockquote>
              <div className="flex items-center gap-3 mb-3">
                <div className="w-12 h-12 bg-gradient-to-r from-green-400 to-green-600 rounded-full flex items-center justify-center">
                  <span className="text-white text-xl">👩‍🚀</span>
                </div>
                <div>
                  <div className="font-bold text-gray-900">Ana Rodríguez</div>
                  <div className="text-sm text-gray-600">Head of Digital</div>
                  <div className="text-xs text-gray-500">HomeDecor Plus</div>
                </div>
              </div>
              <div className="bg-orange-50 border border-orange-200 rounded-lg px-3 py-2">
                <span className="text-xs font-semibold text-orange-700">+35% cross-selling, +28% repeat</span>
              </div>
            </div>
          </div>

          {/* Logos de Plataformas */}
          <div className="text-center">
            <p className="text-sm font-semibold text-purple-600 mb-6">COMPATIBLE CON</p>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6 items-center justify-items-center opacity-60 hover:opacity-80 transition-opacity">
              <div className="flex flex-col items-center justify-center p-3 bg-white border border-gray-200 rounded-lg hover:shadow-md transition-all group">
                <span className="text-2xl mb-1 group-hover:scale-110 transition-transform">🛍️</span>
                <span className="text-xs font-medium text-gray-600 text-center">Shopify Plus</span>
              </div>
              <div className="flex flex-col items-center justify-center p-3 bg-white border border-gray-200 rounded-lg hover:shadow-md transition-all group">
                <span className="text-2xl mb-1 group-hover:scale-110 transition-transform">🛒</span>
                <span className="text-xs font-medium text-gray-600 text-center">WooCommerce</span>
              </div>
              <div className="flex flex-col items-center justify-center p-3 bg-white border border-gray-200 rounded-lg hover:shadow-md transition-all group">
                <span className="text-2xl mb-1 group-hover:scale-110 transition-transform">🏪</span>
                <span className="text-xs font-medium text-gray-600 text-center">Magento</span>
              </div>
              <div className="flex flex-col items-center justify-center p-3 bg-white border border-gray-200 rounded-lg hover:shadow-md transition-all group">
                <span className="text-2xl mb-1 group-hover:scale-110 transition-transform">🏬</span>
                <span className="text-xs font-medium text-gray-600 text-center">PrestaShop</span>
              </div>
            </div>

            <div className="mt-8">
              <div className="inline-flex items-center gap-4 bg-purple-50 border border-purple-200 rounded-full px-6 py-3">
                <span className="text-purple-600 text-lg">✓</span>
                <span className="text-purple-700 font-semibold">Más de 500 tiendas online ya usan Rayuela</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Integración Técnica */}
      <section className="py-14 md:py-16">
        <div className="max-w-7xl mx-auto px-6">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Integración técnica simplificada</h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Para CTOs evaluando el esfuerzo de implementación
            </p>
          </div>

          <div className="card max-w-6xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Arquitectura */}
              <div>
                <h4 className="font-bold text-lg mb-4">Arquitectura de Integración</h4>
                <div className="bg-gray-50 rounded-xl p-6 mb-6">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="bg-blue-100 rounded-lg p-3 text-center flex-1 mr-2">
                        <div className="text-sm font-semibold">Tu E-commerce</div>
                        <div className="text-xs text-gray-600">Frontend + Backend</div>
                      </div>
                      <div className="text-purple-600 font-bold">→</div>
                      <div className="bg-purple-100 rounded-lg p-3 text-center flex-1 ml-2">
                        <div className="text-sm font-semibold">Rayuela API</div>
                        <div className="text-xs text-gray-600">REST + JSON</div>
                      </div>
                    </div>

                    <div className="text-center text-sm text-gray-600">
                      <div className="mb-2 font-semibold">Flujo de datos:</div>
                      <div className="space-y-1 text-xs">
                        <div>1. Envías productos/usuarios/interacciones</div>
                        <div>2. Rayuela entrena modelos automáticamente</div>
                        <div>3. Solicitas recomendaciones via API</div>
                        <div>4. Recibes productos personalizados</div>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="space-y-3">
                  <div className="flex items-center gap-3">
                    <span className="w-5 h-5 text-green-600">✓</span>
                    <span className="text-sm">Sin cambios en tu base de datos</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <span className="w-5 h-5 text-green-600">✓</span>
                    <span className="text-sm">API REST estándar (JSON)</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <span className="w-5 h-5 text-green-600">✓</span>
                    <span className="text-sm">Latencia &lt;100ms garantizada</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <span className="w-5 h-5 text-green-600">✓</span>
                    <span className="text-sm">Escalabilidad automática</span>
                  </div>
                </div>
              </div>

              {/* Ejemplo de código */}
              <div>
                <h4 className="font-bold text-lg mb-4">Ejemplo Request/Response</h4>
                <div className="space-y-4">
                  <div>
                    <h5 className="text-sm font-semibold mb-2 text-gray-600">REQUEST</h5>
                    <div className="bg-gray-900 rounded-lg p-4 text-sm font-mono text-gray-100 overflow-x-auto">
                      <pre>{`POST /api/v1/recommendations
{
  "userId": "user_12345",
  "context": {
    "page": "product",
    "productId": "prod_789"
  },
  "limit": 5,
  "filters": {
    "category": "fashion",
    "inStock": true
  }
}`}</pre>
                    </div>
                  </div>

                  <div>
                    <h5 className="text-sm font-semibold mb-2 text-gray-600">RESPONSE</h5>
                    <div className="bg-gray-900 rounded-lg p-4 text-sm font-mono text-gray-100 overflow-x-auto">
                      <pre>{`{
  "recommendations": [
    {
      "productId": "prod_456",
      "score": 0.95,
      "reason": "frequently_bought_together"
    },
    {
      "productId": "prod_123",
      "score": 0.89,
      "reason": "similar_users_liked"
    }
  ],
  "responseTime": "45ms"
}`}</pre>
                    </div>
                  </div>
                </div>

                <div className="mt-6 bg-green-50 border border-green-200 rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="text-green-600">⚡</span>
                    <span className="font-bold text-green-700">Tiempo de implementación</span>
                  </div>
                  <div className="text-sm text-gray-600 space-y-1">
                    <div>• <strong>Setup inicial:</strong> 2-4 horas</div>
                    <div>• <strong>Integración frontend:</strong> 4-6 horas</div>
                    <div>• <strong>Testing y ajustes:</strong> 2-3 horas</div>
                    <div className="pt-2 border-t border-green-200">
                      <strong className="text-green-700">Total: 1-2 días de desarrollo</strong>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Final */}
      <section className="py-14 md:py-16 section-gradient-strong">
        <div className="max-w-4xl mx-auto px-6 text-center">
          <h2 className="text-3xl font-bold mb-4">
            ¿Listo para aumentar tu AOV como ModaStyle?
          </h2>
          <p className="text-lg text-gray-600 mb-8 max-w-2xl mx-auto">
            Únete a cientos de tiendas online que ya están aumentando sus ventas 
            con recomendaciones inteligentes de Rayuela.
          </p>
          <div className="flex flex-wrap gap-4 justify-center">
            <Link href="/register?utm_source=ecommerce-final" className="btn-primary">
              Empezar gratis ahora
            </Link>
            <Link href="/contact-sales?utm_source=ecommerce-final&industry=ecommerce" className="btn-secondary">
              Hablar con experto en e-commerce
            </Link>
          </div>
          <p className="text-sm text-gray-500 mt-6">
            Sin compromiso • Integración en 48 horas • ROI visible en 30 días
          </p>
        </div>
      </section>
    </main>
  );
}
