import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import Link from "next/link";
import { generateMetadata as generateSEOMetadata } from '@/lib/seo';
import { ArrowLeft, Play, Clock, CheckCircle, ArrowRight, Eye, Heart } from "lucide-react";

export const metadata = generateSEOMetadata({
  title: 'Contenido Digital - Casos de Uso | Rayuela',
  description: 'Retén usuarios con feeds inteligentes y recomendaciones personalizadas. Aumenta tiempo de sesión 22% y engagement en plataformas de contenido.',
  path: '/use-cases/content-digital',
  keywords: ['contenido digital', 'streaming', 'feed', 'personalización', 'engagement', 'retención'],
});

export default function ContentDigitalUseCasePage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-white via-sky-50 to-sky-100 dark:from-gray-950 dark:to-gray-900">
      <div className="container mx-auto px-4 py-16">
        <div className="mb-8">
          <Button variant="ghost" asChild className="mb-4">
            <Link href="/#use-cases">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Volver a casos de uso
            </Link>
          </Button>
          
          <div className="flex items-center gap-3 mb-4">
            <Play className="w-8 h-8 text-primary" />
            <Badge variant="outline" className="text-sm">Contenido Digital</Badge>
          </div>
          
          <h1 className="text-4xl md:text-5xl font-bold text-foreground mb-4">
            Retén usuarios con feeds inteligentes
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl mb-8">
            Transforma tu plataforma de contenido en una experiencia adictiva que mantiene
            a los usuarios enganchados con recomendaciones ultra-personalizadas.
          </p>

          {/* Historia de Éxito */}
          <div className="bg-gradient-to-r from-primary/5 to-accent/5 border border-primary/20 rounded-xl p-8 mb-8">
            <div className="flex items-start gap-4">
              <div className="w-16 h-16 bg-gradient-to-r from-primary to-accent rounded-xl flex items-center justify-center flex-shrink-0">
                <span className="text-white text-2xl">🏆</span>
              </div>
              <div>
                <h3 className="text-xl font-semibold text-foreground mb-3">
                  Caso de Éxito: NewsApp aumentó tiempo de sesión 35% y engagement 50%
                </h3>
                <p className="text-muted-foreground mb-4 leading-relaxed">
                  <strong>NewsApp</strong>, una plataforma de noticias con 500,000 usuarios activos mensuales y 2M artículos,
                  implementó Rayuela para personalizar feeds y recomendaciones. En solo 60 días:
                </p>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="bg-card border border-border rounded-lg p-4 text-center">
                    <div className="text-2xl font-bold text-success mb-1">+35%</div>
                    <div className="text-sm text-muted-foreground">Tiempo de sesión</div>
                  </div>
                  <div className="bg-card border border-border rounded-lg p-4 text-center">
                    <div className="text-2xl font-bold text-primary mb-1">+50%</div>
                    <div className="text-sm text-muted-foreground">Engagement rate</div>
                  </div>
                  <div className="bg-card border border-border rounded-lg p-4 text-center">
                    <div className="text-2xl font-bold text-warning mb-1">+28%</div>
                    <div className="text-sm text-muted-foreground">Daily active users</div>
                  </div>
                </div>
                <blockquote className="mt-4 pl-4 border-l-2 border-primary/30 italic text-muted-foreground">
                  "Rayuela transformó nuestra plataforma. Los usuarios ahora descubren contenido que realmente les interesa.
                  Nuestro engagement se disparó y la retención mejoró dramáticamente."
                  <footer className="mt-2 text-sm">
                    — <strong>Laura Mendoza</strong>, Head of Product en NewsApp
                  </footer>
                </blockquote>
              </div>
            </div>
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid gap-6 md:grid-cols-3 mb-12">
          <Card className="text-center">
            <CardContent className="pt-6">
              <Clock className="w-12 h-12 text-success mx-auto mb-4" />
              <div className="text-3xl font-bold text-success mb-2">+35%</div>
              <p className="text-muted-foreground">Aumento en tiempo de sesión</p>
            </CardContent>
          </Card>
          <Card className="text-center">
            <CardContent className="pt-6">
              <Eye className="w-12 h-12 text-primary mx-auto mb-4" />
              <div className="text-3xl font-bold text-primary mb-2">+50%</div>
              <p className="text-muted-foreground">Mejora en content discovery</p>
            </CardContent>
          </Card>
          <Card className="text-center">
            <CardContent className="pt-6">
              <Heart className="w-12 h-12 text-warning mx-auto mb-4" />
              <div className="text-3xl font-bold text-warning mb-2">+28%</div>
              <p className="text-muted-foreground">Incremento en daily active users</p>
            </CardContent>
          </Card>
        </div>

        {/* Diagrama de Implementación */}
        <Card className="mb-12">
          <CardHeader>
            <CardTitle>Dónde implementar recomendaciones para máximo engagement</CardTitle>
            <CardDescription>
              Puntos estratégicos que mantienen a los usuarios enganchados y descubriendo contenido
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Diagrama Visual */}
              <div className="space-y-6">
                <h4 className="font-semibold text-lg mb-4">Flujo del Usuario</h4>

                {[
                  {
                    step: '1',
                    title: 'Feed Personalizado',
                    description: '"Contenido seleccionado para ti"',
                    impact: '+35% tiempo de sesión',
                    color: 'from-blue-500 to-cyan-500'
                  },
                  {
                    step: '2',
                    title: 'Contenido Relacionado',
                    description: '"Si te gustó esto, también te gustará..."',
                    impact: '+50% content discovery',
                    color: 'from-green-500 to-emerald-500'
                  },
                  {
                    step: '3',
                    title: 'Trending Personalizado',
                    description: '"Trending en tu área de interés"',
                    impact: '+40% engagement rate',
                    color: 'from-purple-500 to-violet-500'
                  },
                  {
                    step: '4',
                    title: 'Notificaciones Inteligentes',
                    description: '"Nuevo contenido que te va a encantar"',
                    impact: '+25% return rate',
                    color: 'from-orange-500 to-red-500'
                  }
                ].map((item) => (
                  <div key={item.step} className="flex items-start gap-4">
                    <div className={`w-10 h-10 bg-gradient-to-r ${item.color} rounded-full flex items-center justify-center text-white font-bold flex-shrink-0`}>
                      {item.step}
                    </div>
                    <div className="flex-1">
                      <h5 className="font-semibold text-foreground mb-1">{item.title}</h5>
                      <p className="text-sm text-muted-foreground mb-2">{item.description}</p>
                      <div className="bg-success/10 border border-success/20 rounded-lg px-3 py-1 inline-block">
                        <span className="text-xs font-medium text-success">{item.impact}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Mockup Visual */}
              <div className="bg-slate-50 dark:bg-slate-800 rounded-xl p-6">
                <h4 className="font-semibold text-lg mb-4">Ejemplo Visual</h4>
                <div className="space-y-4">
                  <div className="bg-white dark:bg-slate-700 rounded-lg p-4 border border-slate-200 dark:border-slate-600">
                    <div className="text-sm font-medium text-foreground mb-2">📱 Feed</div>
                    <div className="text-xs text-muted-foreground">"Para ti"</div>
                    <div className="flex gap-2 mt-2">
                      {['📰', '🎥', '📊'].map((item) => (
                        <div key={item} className="w-8 h-8 bg-primary/10 rounded flex items-center justify-center text-xs">
                          {item}
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="bg-white dark:bg-slate-700 rounded-lg p-4 border border-slate-200 dark:border-slate-600">
                    <div className="text-sm font-medium text-foreground mb-2">🔗 Relacionado</div>
                    <div className="text-xs text-muted-foreground">"También te gustará"</div>
                    <div className="flex gap-2 mt-2">
                      {['📚', '🎧', '🎬'].map((item) => (
                        <div key={item} className="w-8 h-8 bg-accent/10 rounded flex items-center justify-center text-xs">
                          {item}
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="bg-white dark:bg-slate-700 rounded-lg p-4 border border-slate-200 dark:border-slate-600">
                    <div className="text-sm font-medium text-foreground mb-2">🔥 Trending</div>
                    <div className="text-xs text-muted-foreground">"En tu área de interés"</div>
                    <div className="flex gap-2 mt-2">
                      {['⚡', '🚀', '💡'].map((item) => (
                        <div key={item} className="w-8 h-8 bg-warning/10 rounded flex items-center justify-center text-xs">
                          {item}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Explicación Técnica para CTOs */}
        <Card className="mb-12">
          <CardHeader>
            <CardTitle>Integración técnica para plataformas de contenido</CardTitle>
            <CardDescription>
              Para CTOs evaluando personalización de feeds y recomendaciones
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Esquema de Arquitectura */}
              <div>
                <h4 className="font-semibold text-lg mb-4">Arquitectura de Personalización</h4>
                <div className="bg-slate-50 dark:bg-slate-800 rounded-xl p-6">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="bg-blue-100 dark:bg-blue-900 rounded-lg p-3 text-center flex-1 mr-2">
                        <div className="text-sm font-medium">Tu Plataforma</div>
                        <div className="text-xs text-muted-foreground">CMS + Frontend</div>
                      </div>
                      <div className="text-primary">→</div>
                      <div className="bg-primary/10 rounded-lg p-3 text-center flex-1 ml-2">
                        <div className="text-sm font-medium">Rayuela Content</div>
                        <div className="text-xs text-muted-foreground">ML + Feeds</div>
                      </div>
                    </div>

                    <div className="text-center text-sm text-muted-foreground">
                      <div className="mb-2">Flujo de personalización:</div>
                      <div className="space-y-1 text-xs">
                        <div>1. Envías contenido + interacciones</div>
                        <div>2. Rayuela analiza preferencias</div>
                        <div>3. Solicitas feed personalizado</div>
                        <div>4. Recibes contenido rankeado</div>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="mt-6 space-y-3">
                  <div className="flex items-center gap-3">
                    <CheckCircle className="w-5 h-5 text-success" />
                    <span className="text-sm">Compatible con cualquier CMS</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <CheckCircle className="w-5 h-5 text-success" />
                    <span className="text-sm">API REST + webhooks en tiempo real</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <CheckCircle className="w-5 h-5 text-success" />
                    <span className="text-sm">Escalable a millones de contenidos</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <CheckCircle className="w-5 h-5 text-success" />
                    <span className="text-sm">Analytics de engagement incluidos</span>
                  </div>
                </div>
              </div>

              {/* Ejemplo JSON */}
              <div>
                <h4 className="font-semibold text-lg mb-4">Ejemplo Request/Response</h4>
                <div className="space-y-4">
                  <div>
                    <h5 className="text-sm font-medium mb-2 text-muted-foreground">REQUEST</h5>
                    <div className="bg-slate-800 rounded-lg p-4 text-sm font-mono text-slate-100 overflow-x-auto">
                      <pre>{`POST /api/v1/feed
{
  "userId": "user_456",
  "context": {
    "platform": "mobile",
    "location": "feed_home"
  },
  "preferences": {
    "categories": ["tech", "business"],
    "contentTypes": ["article", "video"]
  },
  "limit": 20
}`}</pre>
                    </div>
                  </div>

                  <div>
                    <h5 className="text-sm font-medium mb-2 text-muted-foreground">RESPONSE</h5>
                    <div className="bg-slate-800 rounded-lg p-4 text-sm font-mono text-slate-100 overflow-x-auto">
                      <pre>{`{
  "feed": [
    {
      "contentId": "art_123",
      "score": 0.94,
      "reason": "high_engagement_similar_users",
      "category": "tech",
      "type": "article"
    }
  ],
  "totalItems": 847,
  "responseTime": "41ms"
}`}</pre>
                    </div>
                  </div>
                </div>

                <div className="mt-6 bg-success/10 border border-success/20 rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="text-success">⚡</span>
                    <span className="font-semibold text-success">Tiempo de implementación</span>
                  </div>
                  <div className="text-sm text-muted-foreground space-y-1">
                    <div>• <strong>Setup inicial:</strong> 2-4 horas</div>
                    <div>• <strong>Integración feeds:</strong> 4-6 horas</div>
                    <div>• <strong>Testing y optimización:</strong> 2-4 horas</div>
                    <div className="pt-2 border-t border-success/20">
                      <strong className="text-success">Total: 1-2 días de desarrollo</strong>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Pruebas Sociales - Contenido Digital */}
        <Card className="mb-12">
          <CardHeader>
            <CardTitle>Lo que dicen nuestros clientes de contenido digital</CardTitle>
            <CardDescription>
              Testimonios reales de plataformas que ya están usando Rayuela
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
              {[
                {
                  quote: "Rayuela transformó nuestra plataforma. Los usuarios ahora descubren contenido que realmente les interesa y el engagement se disparó.",
                  author: "Laura Mendoza",
                  role: "Head of Product",
                  company: "NewsApp",
                  avatar: "👩‍💼",
                  metrics: "+35% tiempo sesión, +50% engagement"
                },
                {
                  quote: "Implementamos feeds personalizados en 2 días. Nuestros usuarios pasan 40% más tiempo en la plataforma descubriendo contenido.",
                  author: "Carlos Ruiz",
                  role: "CTO",
                  company: "StreamingPlus",
                  avatar: "👨‍💻",
                  metrics: "+40% tiempo plataforma, +30% retención"
                },
                {
                  quote: "Las recomendaciones de Rayuela son tan precisas que nuestros usuarios sienten que la app los conoce. El engagement nunca había sido tan alto.",
                  author: "Ana Torres",
                  role: "Product Manager",
                  company: "PodcastHub",
                  avatar: "👩‍🚀",
                  metrics: "+55% content discovery, +25% DAU"
                }
              ].map((testimonial) => (
                <div
                  key={testimonial.author}
                  className="bg-card border border-border rounded-xl p-6 hover:shadow-lg transition-all duration-300"
                >
                  <blockquote className="text-muted-foreground mb-4 leading-relaxed italic">
                    "{testimonial.quote}"
                  </blockquote>
                  <div className="flex items-center gap-3 mb-3">
                    <div className="w-12 h-12 bg-gradient-to-r from-primary/20 to-accent/20 rounded-full flex items-center justify-center">
                      <span className="text-xl">{testimonial.avatar}</span>
                    </div>
                    <div>
                      <div className="font-semibold text-foreground">{testimonial.author}</div>
                      <div className="text-sm text-muted-foreground">{testimonial.role}</div>
                      <div className="text-xs text-muted-foreground/70">{testimonial.company}</div>
                    </div>
                  </div>
                  <div className="bg-success/10 border border-success/20 rounded-lg px-3 py-2">
                    <span className="text-xs font-medium text-success">{testimonial.metrics}</span>
                  </div>
                </div>
              ))}
            </div>

            {/* Logos de Plataformas */}
            <div className="text-center mb-6">
              <p className="text-sm font-semibold text-primary mb-4">TIPOS DE CONTENIDO COMPATIBLES</p>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-6 items-center justify-items-center opacity-60 hover:opacity-80 transition-opacity">
                {[
                  { name: 'Artículos', icon: '📰' },
                  { name: 'Videos', icon: '🎥' },
                  { name: 'Podcasts', icon: '🎧' },
                  { name: 'Imágenes', icon: '📸' },
                ].map((content) => (
                  <div
                    key={content.name}
                    className="flex flex-col items-center justify-center p-3 bg-card border border-border rounded-lg hover:shadow-md transition-all group"
                  >
                    <span className="text-2xl mb-1 group-hover:scale-110 transition-transform">{content.icon}</span>
                    <span className="text-xs font-medium text-muted-foreground text-center">{content.name}</span>
                  </div>
                ))}
              </div>
            </div>

            <div className="text-center">
              <div className="inline-flex items-center gap-4 bg-primary/10 border border-primary/20 rounded-full px-6 py-3">
                <span className="text-primary text-lg">✓</span>
                <span className="text-primary font-semibold">Más de 300 plataformas de contenido ya usan Rayuela</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* CTA Contextual para Contenido Digital */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
          {/* CTA Técnico */}
          <Card className="border-primary/20 bg-gradient-to-br from-primary/5 to-primary/10">
            <CardContent className="pt-8">
              <div className="text-center">
                <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">🚀</span>
                </div>
                <h3 className="text-xl font-bold mb-3">
                  Para CTOs & Developers
                </h3>
                <p className="text-muted-foreground mb-6">
                  Prueba feeds personalizados con tu contenido actual.
                  API de recomendaciones y documentación completa.
                </p>
                <Button asChild size="lg" className="w-full">
                  <Link href="/register?utm_source=content-tech">
                    Empezar integración técnica
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Link>
                </Button>
                <p className="text-xs text-muted-foreground mt-3">
                  API keys gratis • Sandbox completo • Soporte técnico
                </p>
              </div>
            </CardContent>
          </Card>

          {/* CTA Comercial */}
          <Card className="border-accent/20 bg-gradient-to-br from-accent/5 to-accent/10">
            <CardContent className="pt-8">
              <div className="text-center">
                <div className="w-16 h-16 bg-accent/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">💼</span>
                </div>
                <h3 className="text-xl font-bold mb-3">
                  Para Product Owners & CMOs
                </h3>
                <p className="text-muted-foreground mb-6">
                  Ve una demo con datos de tu tipo de contenido.
                  Calculamos el impacto en engagement y retención.
                </p>
                <Button asChild variant="outline" size="lg" className="w-full border-accent/30 hover:bg-accent/5">
                  <Link href="/contact-sales?utm_source=content-business&industry=content">
                    Solicitar demo de contenido
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Link>
                </Button>
                <p className="text-xs text-muted-foreground mt-3">
                  Demo en 30 min • Análisis de engagement • Casos similares
                </p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* CTA Final Unificado */}
        <Card className="bg-gradient-to-r from-primary/10 to-accent/10 border-primary/20">
          <CardContent className="pt-8">
            <div className="text-center">
              <h3 className="text-2xl font-bold mb-4">
                ¿Listo para crear feeds adictivos como NewsApp?
              </h3>
              <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
                Únete a más de 300 plataformas de contenido que ya usan Rayuela para mantener
                a sus usuarios enganchados más tiempo y descubriendo contenido relevante.
              </p>
              <div className="flex gap-4 justify-center flex-wrap">
                <Button asChild size="lg">
                  <Link href="/register?utm_source=content-final">
                    Empezar gratis ahora
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Link>
                </Button>
                <Button variant="outline" asChild size="lg">
                  <Link href="/contact-sales?utm_source=content-final&industry=content">
                    Hablar con experto en contenido
                  </Link>
                </Button>
              </div>
              <p className="text-xs text-muted-foreground mt-4">
                Sin compromiso • Integración en 48 horas • Métricas en tiempo real
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
