#!/bin/bash

# 🚨 SCRIPT DE SEGURIDAD CRÍTICA: Actualizar SECRET_KEY en Google Cloud Secret Manager
# 
# Este script debe ejecutarse INMEDIATAMENTE para rotar la SECRET_KEY comprometida
# 
# IMPORTANTE: La SECRET_KEY anterior fue expuesta en el repositorio y debe ser rotada

set -e

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${RED}🚨 ROTACIÓN DE SECRET_KEY CRÍTICA${NC}"
echo "=================================="
echo ""

# Nueva SECRET_KEY generada de forma segura
NEW_SECRET_KEY="MCKMnkpKAOwPyb8wyJ-PqiTZSGdNHMnz84ALGtwXVXvUheOM1yx4DxBdwk_AIJFz-_gQgYJlUBZ08za2g0voKg=="

echo -e "${YELLOW}⚠️  CONTEXTO DE SEGURIDAD:${NC}"
echo "- La SECRET_KEY anterior fue expuesta en temp_secret_key.txt"
echo "- Esta exposición permite forjar JWTs y comprometer la autenticación"
echo "- Se requiere rotación INMEDIATA en todos los entornos"
echo ""

echo -e "${BLUE}📋 PASOS A EJECUTAR:${NC}"
echo ""

echo "1. 🔐 Crear/actualizar secreto en Secret Manager:"
echo "   gcloud secrets create jwt-secret-key --replication-policy=automatic || echo 'Secret ya existe'"
echo "   echo -n '${NEW_SECRET_KEY}' | gcloud secrets versions add jwt-secret-key --data-file=-"
echo ""

echo "2. 🔄 Actualizar Cloud Run para usar el nuevo secreto:"
echo "   gcloud run services update rayuela-backend \\"
echo "     --set-secrets=SECRET_KEY=jwt-secret-key:latest \\"
echo "     --region=us-central1"
echo ""

echo "3. 🔄 Actualizar workers/jobs para usar el nuevo secreto:"
echo "   gcloud run jobs update rayuela-worker \\"
echo "     --set-secrets=SECRET_KEY=jwt-secret-key:latest \\"
echo "     --region=us-central1"
echo ""

echo "4. ✅ Verificar que el secreto se actualizó:"
echo "   gcloud secrets versions list jwt-secret-key"
echo ""

echo "5. 🧪 Probar que los servicios funcionan con el nuevo secreto:"
echo "   curl -s https://TU_BACKEND_URL/health"
echo ""

echo -e "${RED}⚠️  IMPORTANTE:${NC}"
echo "- Ejecuta estos comandos INMEDIATAMENTE"
echo "- Verifica que todos los servicios funcionen después del cambio"
echo "- Revisa logs de autenticación para detectar actividad sospechosa"
echo "- Considera invalidar todas las sesiones activas si es necesario"
echo ""

echo -e "${GREEN}💡 COMANDOS LISTOS PARA COPIAR:${NC}"
echo "=================================="
echo ""
echo "# Crear/actualizar secreto"
echo "gcloud secrets create jwt-secret-key --replication-policy=automatic || echo 'Secret ya existe'"
echo "echo -n '${NEW_SECRET_KEY}' | gcloud secrets versions add jwt-secret-key --data-file=-"
echo ""
echo "# Actualizar servicios (ajusta los nombres según tu configuración)"
echo "gcloud run services update rayuela-backend --set-secrets=SECRET_KEY=jwt-secret-key:latest --region=us-central1"
echo "gcloud run jobs update rayuela-worker --set-secrets=SECRET_KEY=jwt-secret-key:latest --region=us-central1"
echo ""
echo "# Verificar"
echo "gcloud secrets versions list jwt-secret-key"
echo ""

echo -e "${BLUE}📝 DOCUMENTACIÓN:${NC}"
echo "- Esta rotación resuelve la vulnerabilidad P0 identificada"
echo "- La nueva SECRET_KEY tiene 512 bits de entropía (segura)"
echo "- Generada con el módulo secrets de Python (criptográficamente segura)"
echo ""
