from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from typing import List, Optional
from datetime import datetime
from src.core.deps import get_current_account, get_current_admin_user
from src.core.redis_manager import RedisManager
from src.db import schemas
from src.db.session import get_db
from src.utils.base_logger import log_info, log_error
from src.core.exceptions import (
    AccountNotFoundError,
    ResourceNotFoundError,
)
from src.db.models.account import Account
from src.db.models.system_user import SystemUser
from src.db.models.audit_log import AuditLog
from src.db.repositories import AccountRepository

router = APIRouter()


@router.get("/", response_model=List[schemas.AccountResponse])
async def list_accounts(db: AsyncSession = Depends(get_db)):
    """List all accounts."""
    repository = AccountRepository(db)
    accounts = await repository.get_all()
    return accounts


@router.post("/accounts", response_model=schemas.AccountResponse)
async def create_account(
    account_create: schemas.AccountCreate, db: AsyncSession = Depends(get_db)
):
    """
    Crea una nueva cuenta.

    Utiliza la columna Identity para generar automáticamente el ID de la cuenta.
    """
    try:
        # Crear la cuenta en una transacción
        async with db.begin():
            # Crear la cuenta dejando que la columna Identity genere el ID
            repository = AccountRepository(db)
            account = await repository.create(account_create)

            # El ID se genera automáticamente por la columna Identity
            log_info(f"Cuenta creada exitosamente con ID: {account.account_id}")

        # El commit ya se ha realizado automáticamente
        return account

    except ValueError as e:
        log_error(f"Error de validación al crear cuenta: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        log_error(f"Error inesperado al crear cuenta: {str(e)}")
        raise HTTPException(status_code=500, detail="Error interno del servidor")


@router.get("/{account_id}", response_model=schemas.AccountResponse)
async def get_account(
    account_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: SystemUser = Depends(get_current_admin_user),
):
    """Get account by ID."""
    # Nota: Este endpoint es solo para administradores, por lo que no necesitamos verificar
    # que el account_id pertenezca al usuario actual. Sin embargo, verificamos que la cuenta exista.
    account = await db.get(Account, account_id)
    if not account:
        raise AccountNotFoundError(account_id)
    return account


@router.patch(
    "/{account_id}/deactivate", status_code=status.HTTP_204_NO_CONTENT
)  # 204 No Content para éxito sin cuerpo
async def deactivate_account(
    account_id: int,
    db: AsyncSession = Depends(get_db),
    # Podrías necesitar current_user para autorización aquí
):
    # Usar el repositorio o lógica directa
    repo = AccountRepository(db)  # No necesita sub_repo para esto
    try:
        async with db.begin():
            account_to_deactivate = await repo.delete(
                account_id
            )  # Llama al método corregido
            # O lógica directa:
            # account_to_deactivate = await db.get(Account, account_id)
            # if account_to_deactivate:
            #     account_to_deactivate.is_active = False

            if not account_to_deactivate:
                raise AccountNotFoundError(account_id)  # Lanza 404

            # Podrías hacer otras cosas relacionadas, como revocar API keys
            # account_to_deactivate.api_key = None

        # --- Commit automático ---
        log_info(f"Account {account_id} deactivated successfully.")
        # No se retorna cuerpo con 204

    except AccountNotFoundError:
        raise  # Relanzar 404
    except HTTPException:
        raise
    except Exception as e:
        log_error(
            f"Unexpected error deactivating account {account_id}: {e}", exc_info=True
        )
        raise  # Dejar que el middleware maneje el 500


@router.patch("/{account_id}/activate", response_model=schemas.AccountResponse)
async def activate_account(account_id: int, db: AsyncSession = Depends(get_db)):
    """Activate an account."""
    try:
        async with db.begin():
            repository = AccountRepository(db)
            account = await repository.get_by_id(account_id)
            if not account:
                raise HTTPException(status_code=404, detail="Account not found")

            account.is_active = True
            # No es necesario llamar a repository.update() ya que estamos modificando
            # directamente el objeto de la sesión y el commit se hará automáticamente
            # al salir del bloque async with

        log_info(f"Account {account_id} activated successfully.")
        return account
    except HTTPException:
        raise
    except Exception as e:
        log_error(
            f"Unexpected error activating account {account_id}: {e}", exc_info=True
        )
        raise


# API Key management endpoints have been moved to /api-keys/
# These endpoints are deprecated and removed to avoid confusion.
# Use the following endpoints instead:
# - POST /api/v1/api-keys/ - Create/regenerate API key
# - GET /api/v1/api-keys/current - Get API key metadata
# - DELETE /api/v1/api-keys/ - Revoke API key


@router.get("/current", response_model=schemas.AccountResponse)
async def get_account_info(
    account: schemas.AccountResponse = Depends(get_current_account),
    db: AsyncSession = Depends(get_db),
):
    """Get information about the current account."""
    # Obtener información de la cuenta
    account_data = account.model_dump()
    
    try:
        # Obtener la suscripción activa para esta cuenta
        from src.services.subscription_service import SubscriptionService
        subscription_service = SubscriptionService(db)
        subscription = await subscription_service.get_active_subscription(account.account_id)
        
        # Si existe una suscripción activa, añadirla a la respuesta
        if subscription:
            account_data["subscription"] = {
                "plan": subscription.plan_type,
                "is_active": subscription.is_active,
                "expires_at": subscription.expires_at
            }
    except Exception as e:
        # Si hay un error al obtener la suscripción, no fallamos la request
        # simplemente no incluimos la información de suscripción
        from src.utils.base_logger import log_error
        log_error(f"Error getting subscription for account {account.account_id}: {str(e)}")
    
    return account_data


@router.put("/current", response_model=schemas.AccountResponse)
async def update_current_account(
    account_update: schemas.AccountUpdate,
    account: schemas.AccountResponse = Depends(get_current_account),
    db: AsyncSession = Depends(get_db),
    current_user: SystemUser = Depends(get_current_admin_user),  # Solo administradores pueden actualizar la cuenta
):
    """Update the current account information.

    This endpoint allows administrators to update their own account details,
    such as the account name.
    """
    try:
        async with db.begin():
            # Usar el repositorio para actualizar la cuenta
            repository = AccountRepository(db)
            updated_account = await repository.update(account.account_id, account_update)

            if not updated_account:
                raise HTTPException(status_code=404, detail="Account not found")

            # Registrar la acción en el log
            log_info(f"Account {account.account_id} updated by user {current_user.id}")

            return updated_account
    except HTTPException:
        raise
    except Exception as e:
        log_error(f"Error updating account {account.account_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while updating the account"
        )


@router.patch("/current", response_model=schemas.AccountResponse)
async def patch_current_account(
    account_update: schemas.AccountUpdate,
    account: schemas.AccountResponse = Depends(get_current_account),
    db: AsyncSession = Depends(get_db),
    current_user: SystemUser = Depends(get_current_admin_user),  # Solo administradores pueden actualizar la cuenta
):
    """Partially update the current account information.

    This endpoint allows administrators to update specific fields of their own account,
    such as the account name, without having to provide all fields.
    """
    # Reutilizamos la misma lógica que el endpoint PUT
    # La diferencia es que PUT se usa para actualizar todos los campos,
    # mientras que PATCH se usa para actualizaciones parciales
    return await update_current_account(account_update, account, db, current_user)


@router.get("/{account_id}/audit-logs", response_model=List[schemas.AuditLog])
async def get_audit_logs(
    account_id: int,
    action: Optional[str] = None,
    artifact_name: Optional[str] = None,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    db: AsyncSession = Depends(get_db),
    current_user: SystemUser = Depends(get_current_admin_user),
):
    """Get audit logs with optional filters for a specific account."""
    # Nota: Este endpoint es solo para administradores, por lo que no necesitamos verificar
    # que el account_id pertenezca al usuario actual. Sin embargo, verificamos que la cuenta exista.
    account = await db.get(Account, account_id)
    if not account:
        raise AccountNotFoundError(account_id)

    if not (action or artifact_name or start_date or end_date):
        raise ResourceNotFoundError(
            resource_type="AuditLog",
            resource_id="N/A",
            detail="At least one filter must be provided",
        )

    query = select(AuditLog).where(AuditLog.account_id == account_id)

    if action:
        query = query.where(AuditLog.action == action)

    if artifact_name:
        query = query.where(AuditLog.artifact_name == artifact_name)

    if start_date:
        query = query.where(AuditLog.timestamp >= start_date)

    if end_date:
        query = query.where(AuditLog.timestamp <= end_date)

    result = await db.execute(query)
    logs = result.scalars().all()
    return logs


@router.get("/usage", response_model=schemas.UsageStats)
async def get_api_usage(
    account: schemas.AccountResponse = Depends(get_current_account),
    db: AsyncSession = Depends(get_db),
):
    """Get API usage statistics for the current account."""
    redis_manager = await RedisManager.get_instance()
    usage_stats = await redis_manager.get(f"{account.api_key}_usage") or {}
    return usage_stats
