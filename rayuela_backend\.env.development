# Rayuela Backend Environment Variables - Development Local
# Generated for local development on Windows 11 with miniconda

# =============================================================================
# ENVIRONMENT CONFIGURATION
# =============================================================================
ENV=development
DEBUG=true
LOG_LEVEL=INFO

# =============================================================================
# SERVER CONFIGURATION
# =============================================================================
API_HOST=0.0.0.0
API_PORT=8001

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
SECRET_KEY=nuyIPCzYuhrQiu_C0frtrXbUAqGNhGDkXTTrvqD-_8vnvSUN6CiKqgeohYe66i4VfGKUQ6cSW5_jCX89T4-5jQ==
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
API_KEY_PREFIX=ray_

# =============================================================================
# DATABASE CONFIGURATION (PostgreSQL local)
# =============================================================================
POSTGRES_USER=postgres
POSTGRES_PASSWORD=rayuelasql
POSTGRES_SERVER=localhost
POSTGRES_PORT=5432
POSTGRES_DB=rayuela_dev

# =============================================================================
# REDIS CONFIGURATION (Disabled for local development)
# =============================================================================
# Redis not installed locally - using memory cache fallback
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_URL=redis://localhost:6379/0
CACHE_EXPIRE_TIME=30
DISABLE_REDIS=true

# =============================================================================
# DEVELOPMENT FLAGS
# =============================================================================
SKIP_SECRETS=true
SKIP_MIGRATIONS=false
TEMP_BACKEND_MODE=false

# =============================================================================
# API SETTINGS
# =============================================================================
RATE_LIMIT=100
RATE_LIMIT_PERIOD=60

# =============================================================================
# CORS CONFIGURATION
# =============================================================================
ALLOWED_ORIGINS=["http://localhost:3000", "http://127.0.0.1:3000", "*"]
ALLOWED_HOSTS=["localhost", "127.0.0.1", "0.0.0.0"]

# =============================================================================
# EXTERNAL SERVICES (Development - Optional)
# =============================================================================
MERCADOPAGO_ACCESS_TOKEN=TEST-your-test-token-here
MERCADOPAGO_PUBLIC_KEY=TEST-your-public-key-here
MERCADOPAGO_WEBHOOK_SECRET=your-webhook-secret
PAYMENT_GATEWAY=mercadopago

# =============================================================================
# GCP CONFIGURATION (Optional for local development)
# =============================================================================
GCP_PROJECT_ID=rayuela-dev
GCP_REGION=us-central1
GCS_BUCKET_NAME=rayuela-dev-bucket

# =============================================================================
# FRONTEND URL
# =============================================================================
FRONTEND_URL=http://localhost:3000

# =============================================================================
# MONITORING
# =============================================================================
LATENCY_THRESHOLD=1.0
MONITORING_INTERVAL=60
