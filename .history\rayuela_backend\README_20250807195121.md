# Rayuela - Sistema de Recomendación como Servicio

**Copyright (c) [2025] [rayuela.ai]. Todos los derechos reservados.**

**Bienvenido/a a Rayuela, el corazón de nuestra plataforma de recomendaciones B2B.**

## Cambios Recientes

### Estandarización de Timestamps con server_default=func.now()

Se ha realizado una refactorización para estandarizar el uso de `server_default=func.now()` y `onupdate=func.now()` para los campos `created_at` y `updated_at` en todos los modelos relevantes, asegurando que todos usen `DateTime(timezone=True)`. Esta mejora proporciona:

1. **Mayor Robustez**: Al delegar la generación del timestamp a la base de datos, se asegura que los timestamps sean consistentes incluso en sistemas distribuidos o con múltiples workers.
2. **Consistencia**: Todos los modelos ahora utilizan el mismo enfoque para manejar timestamps.
3. **Precisión**: Al usar `timezone=True`, se asegura que los timestamps incluyan información de zona horaria, evitando ambigüedades.
4. **Integridad de Datos**: Los timestamps se generan automáticamente a nivel de base de datos, incluso si se omiten en el código.

Para aplicar estos cambios a una base de datos existente, ejecuta:

```bash
# Si tienes alembic instalado globalmente
alembic upgrade head

# O si prefieres usar Python directamente
python -m alembic upgrade head

# Si estás usando Docker
docker-compose exec api alembic upgrade head
```

### Uso de Enums en lugar de Strings para Tipos Fijos

Se ha realizado una refactorización para utilizar SQLAlchemy Enums (SQLAEnum) en lugar de Strings para los campos que representan tipos fijos:

- `Role.name`: Ahora utiliza `SQLAEnum(RoleType)` en lugar de `String(100)`
- `Permission.name`: Ahora utiliza `SQLAEnum(PermissionType)` en lugar de `String`
- `Subscription.plan_type`: Ahora utiliza `SQLAEnum(SubscriptionPlan)` en lugar de `String`

Esta mejora proporciona:

1. **Mayor Consistencia**: Todos los campos que representan tipos fijos utilizan Enums de SQLAlchemy.
2. **Validación a Nivel de DB**: Al usar SQLAEnum, se crea un tipo ENUM en la base de datos, lo que proporciona validación a nivel de DB.
3. **Claridad Semántica**: El código es más legible y semánticamente claro, ya que los valores posibles están definidos explícitamente.
4. **Seguridad de Tipos**: El sistema de tipos ahora puede verificar que los valores utilizados son válidos en tiempo de compilación.

Para aplicar estos cambios a una base de datos existente, ejecuta:

```bash
# Si tienes alembic instalado globalmente
alembic upgrade head

# O si prefieres usar Python directamente
python -m alembic upgrade head

# Si estás usando Docker
docker-compose exec api alembic upgrade head
```

## 🔧 Migración de Tipos de ID

**IMPORTANTE**: Si estás actualizando desde una versión anterior del proyecto, necesitas ejecutar la migración de unificación de tipos de ID para resolver inconsistencias críticas en la base de datos.

```bash
# Ejecutar la migración completa (con validaciones)
python run_id_migration.py

# O ejecutar paso a paso:
python validate_id_migration.py        # Pre-validación
alembic upgrade unify_entity_id_types   # Migración
python post_migration_validation.py    # Post-validación
```

Ver `ID_MIGRATION_README.md` para documentación completa del proceso.

## ¿Qué es Rayuela?

Imagina que tienes una tienda online, una plataforma de streaming o cualquier aplicación donde muestras productos, artículos o contenido a tus usuarios. ¿No sería genial poder mostrarles exactamente lo que les podría interesar más, aumentando así la interacción y las ventas?

Eso es lo que hace Rayuela. Es un **servicio "cerebro"** que las empresas pueden usar para obtener recomendaciones personalizadas para *sus* usuarios finales. Funciona como un **servicio de alquiler de inteligencia**:

1.  Nuestros clientes (otras empresas) nos envían información sobre sus productos, sus usuarios y cómo interactúan estos usuarios con los productos (clics, compras, visualizaciones).
2.  Nuestra API procesa esta información y entrena "modelos" (pequeños cerebros artificiales) que aprenden los patrones de gustos y preferencias.
3.  Cuando el cliente nos lo pide a través de nuestra API, le devolvemos una lista de recomendaciones personalizadas para un usuario específico o basadas en un producto determinado.

Este proyecto es el **backend** (la parte "invisible" que hace todo el trabajo pesado) de ese servicio. Es una **API RESTful**, lo que significa que otras aplicaciones pueden comunicarse con ella a través de internet usando un lenguaje estándar (HTTP), de forma similar a como tu navegador web pide páginas a un servidor.

**Público Objetivo:** Empresas (B2B) que necesitan añadir capacidades de recomendación a sus propias plataformas (tiendas online, apps móviles, plataformas de contenido, herramientas internas) sin tener que construir toda la tecnología desde cero.

**Modelo de Negocio:** API-first SaaS (Software como Servicio). Los clientes se suscriben a diferentes planes que les dan acceso a la API con ciertos límites de uso (cantidad de recomendaciones, productos, usuarios, frecuencia de entrenamiento de modelos).

## Inicio Rápido

Para comenzar a utilizar el sistema de recomendación rápidamente, consulta nuestra [Guía de Inicio Rápido](QUICKSTART.md) que te guiará a través de los primeros pasos: registro, obtención de API Key, carga de datos y obtención de recomendaciones.

## Características Principales

*   **API RESTful Moderna:** Construida con [FastAPI](https://fastapi.tiangolo.com/), lo que la hace rápida, fácil de usar y con documentación automática.
*   **Multi-Tenant:** Diseñada para servir a múltiples clientes (cuentas) de forma segura y aislada desde la misma infraestructura. La base de datos está particionada por `account_id`.
*   **Autenticación Segura:**
    *   **API Keys:** Para que las aplicaciones de nuestros clientes se autentiquen (por cuenta).
    *   **JWT (Tokens):** Para que los usuarios administradores de nuestros clientes gestionen su cuenta a través de un futuro panel de control (por usuario del sistema).
*   **Modelos de Recomendación Híbridos:** Combina técnicas de "Collaborative Filtering" (aprender de las interacciones de *todos* los usuarios) y "Content-Based" (aprender de las características de los *productos*).
*   **Entrenamiento Asíncrono:** El proceso de "entrenar" los modelos (que puede llevar tiempo) se realiza en segundo plano usando [Celery](https://docs.celeryq.dev/en/stable/), para no bloquear la API principal.
*   **Gestión de Límites de Uso:** Integrado con los planes de suscripción para controlar cuántas llamadas a la API, productos, usuarios, etc., puede usar cada cliente.
*   **Almacenamiento Eficiente:** Guarda los modelos entrenados en [Google Cloud Storage](https://cloud.google.com/storage) (o localmente para desarrollo).
*   **Base de Datos Robusta:** Utiliza [PostgreSQL](https://www.postgresql.org/), una base de datos fiable y potente.
*   **Caché Rápida:** Usa [Redis](https://redis.io/) para acelerar respuestas frecuentes y gestionar tareas como el rate limiting (limitar cuántas peticiones se pueden hacer en un tiempo).
*   **Preparada para la Nube:** Diseñada para desplegarse fácilmente en servicios como [Google Cloud Run](https://cloud.google.com/run).

## Arquitectura Simplificada

Puedes ver un diagrama detallado de la arquitectura [aquí](docs\ARCHITECTURE.md).

En términos sencillos, el flujo es así:

1.  **Cliente (App del Cliente)**: Envía una petición a nuestra API (ej: "dame recomendaciones para el usuario 'X'").
2.  **API (FastAPI en Cloud Run)**: Recibe la petición. Verifica la autenticación (API Key).
3.  **Middleware**: Realiza tareas comunes (logs, seguridad, límites de velocidad).
4.  **Router**: Dirige la petición a la función correcta.
5.  **Endpoint Logic**:
    *   Consulta **Redis (Caché)**: ¿Ya tenemos recomendaciones recientes para 'X'? Si sí, las devuelve rápido.
    *   Si no, consulta **Servicios**: Llama al servicio de recomendaciones.
    *   **Servicio de Recomendaciones**: Carga el modelo relevante desde **Google Cloud Storage**. Consulta datos necesarios de **PostgreSQL (Base de Datos)**. Genera las recomendaciones.
    *   Guarda las nuevas recomendaciones en **Redis (Caché)** para futuras peticiones.
6.  **API**: Devuelve la respuesta (la lista de recomendaciones) al Cliente.

Para el **entrenamiento**, el flujo es diferente:

1.  **Cliente (o un proceso automático)**: Envía una petición a la API (ej: "entrena los modelos para la cuenta 'Y'").
2.  **API**: Verifica permisos. Envía una tarea a **Celery (Cola de Tareas)** usando **Redis** como intermediario. Responde al cliente "OK, entrenamiento iniciado".
3.  **Worker (Celery en un Servidor)**: Recoge la tarea de la cola.
4.  **Worker**: Llama al **Servicio de Recomendaciones** para ejecutar el entrenamiento.
5.  **Servicio de Recomendaciones**: Carga datos de **PostgreSQL**, entrena los modelos (puede tardar minutos u horas).
6.  **Worker**: Guarda los nuevos modelos en **Google Cloud Storage**. Actualiza el estado del entrenamiento en **PostgreSQL**.

## Stack Tecnológico

*   **Backend Framework:** [FastAPI](https://fastapi.tiangolo.com/) (Python)
*   **Base de Datos:** [PostgreSQL](https://www.postgresql.org/) (>= 13)
*   **Caché / Broker de Mensajes:** [Redis](https://redis.io/)
*   **Tareas Asíncronas:** [Celery](https://docs.celeryq.dev/en/stable/)
*   **Migraciones de Base de Datos:** [Alembic](https://alembic.sqlalchemy.org/en/latest/) (integrado con SQLAlchemy)
*   **ORM (Interacción con BD):** [SQLAlchemy](https://www.sqlalchemy.org/) (Async version with AsyncPG)
*   **Validación de Datos:** [Pydantic](https://pydantic-docs.helpmanual.io/) (usado por FastAPI)
*   **Almacenamiento de Objetos (Modelos):** [Google Cloud Storage](https://cloud.google.com/storage) (o sistema de archivos local)
*   **Contenedorización:** [Docker](https://www.docker.com/)
*   **Plataforma de Despliegue (Inicial):** [Google Cloud Run](https://cloud.google.com/run)

## Puesta en Marcha (Desarrollo Local)

¿Quieres ejecutar la API en tu propia computadora para probarla o desarrollarla? ¡Sigue estos pasos!

**Requisitos Previos:**

1.  **Git:** Para descargar el código fuente. [Instalar Git](https://git-scm.com/book/en/v2/Getting-Started-Installing-Git).
2.  **Python:** Versión 3.9 o superior. [Instalar Python](https://www.python.org/downloads/). Asegúrate de que `pip` (el instalador de paquetes de Python) esté incluido.
3.  **Docker y Docker Compose:** La forma más sencilla de ejecutar PostgreSQL y Redis localmente sin instalarlos directamente en tu sistema. [Instalar Docker Desktop](https://www.docker.com/products/docker-desktop/).

**Pasos:**

1.  **Clonar el Repositorio:**
    Abre tu terminal o línea de comandos y ejecuta:
    ```bash
    git clone https://github.com/TU_USUARIO/rayuela.git # Reemplaza con la URL real
    cd rayuela
    ```

2.  **Crear un Entorno Virtual:**
    Es una buena práctica aislar las dependencias de cada proyecto.
    ```bash
    python -m venv venv
    ```
    Activa el entorno:
    *   En macOS/Linux: `source venv/bin/activate`
    *   En Windows: `.\venv\Scripts\activate`
    Verás `(venv)` al principio de la línea de comandos si se activó correctamente.

3.  **Instalar Dependencias:**
    Instala todas las librerías Python necesarias.
    ```bash
    pip install -r requirements.txt
    ```
    (Puede que también necesites un `requirements-dev.txt` si existe para herramientas de desarrollo).

4.  **Configurar Servicios Externos (PostgreSQL y Redis):**
    Usaremos Docker Compose para esto. Asegúrate de que Docker Desktop esté corriendo.
    *   Busca un archivo llamado `docker-compose.yml` o `docker-compose.local.yml` en el proyecto. Si no existe, deberás crearlo (puedes pedir ayuda para esto). Un ejemplo básico podría ser:
        ```yaml
        # docker-compose.yml
        version: '3.8'
        services:
          db:
            image: postgres:15-alpine
            environment:
              POSTGRES_DB: rayuela_dev
              POSTGRES_USER: rayuela_user
              POSTGRES_PASSWORD: supersecretpassword # ¡Cambia esto en producción!
            ports:
              - "5432:5432" # Expone el puerto de la BD a tu máquina local
            volumes:
              - postgres_data:/var/lib/postgresql/data/
          cache:
            image: redis:7-alpine
            ports:
              - "6379:6379" # Expone el puerto de Redis a tu máquina local
            volumes:
              - redis_data:/data

        volumes:
          postgres_data:
          redis_data:
        ```
    *   Desde la carpeta raíz del proyecto, ejecuta:
        ```bash
        docker-compose up -d
        ```
        Esto descargará las imágenes de PostgreSQL y Redis (si no las tienes) y las iniciará en segundo plano (`-d`).

5.  **Configurar Variables de Entorno:**
    La aplicación necesita saber cómo conectarse a la base de datos, Redis, etc. Esto se hace mediante variables de entorno.
    *   Busca un archivo llamado `.env.example` o similar.
    *   Cópialo a un nuevo archivo llamado `.env`:
        ```bash
        cp .env.example .env
        ```
    *   Abre el archivo `.env` con un editor de texto y **rellena los valores correctos**. Presta especial atención a:
        *   `DATABASE_URL`: Debería coincidir con los datos del `docker-compose.yml` (ej: `postgresql+asyncpg://rayuela_user:supersecretpassword@localhost:5432/rayuela_dev`)
        *   `REDIS_URL`: (ej: `redis://localhost:6379/0`)
        *   `SECRET_KEY`: Una cadena larga y aleatoria para seguridad de tokens.
        *   `ALGORITHM`: Usualmente `HS256` para JWT.
        *   `ACCESS_TOKEN_EXPIRE_MINUTES`: Tiempo de validez de los tokens.
        *   `MODEL_STORAGE_TYPE`: Ponlo en `local` para desarrollo.
        *   `LOCAL_MODEL_STORAGE_PATH`: Una carpeta en tu máquina donde guardar modelos (ej: `./local_models`). Crea esta carpeta.

6.  **Aplicar Migraciones de Base de Datos:**
    Esto crea las tablas y estructuras necesarias en la base de datos PostgreSQL que acabas de iniciar con Docker.
    ```bash
    alembic upgrade head
    ```
    (Si no usas Alembic, puede haber un script diferente o instrucciones manuales).

7.  **¡Ejecutar la API!**
    Usa Uvicorn, un servidor ASGI rápido, para lanzar la aplicación FastAPI.
    ```bash
    uvicorn main:app --reload --host 0.0.0.0 --port 8000
    ```
    *   `main:app`: Le dice a Uvicorn dónde encontrar la aplicación FastAPI (en el archivo `main.py`, la variable se llama `app`).
    *   `--reload`: Reinicia el servidor automáticamente si cambias el código (¡muy útil para desarrollo!).
    *   `--host 0.0.0.0`: Permite acceder a la API desde otras máquinas en tu red local (opcional).
    *   `--port 8000`: El puerto en el que escuchará la API.

8.  **Acceder a la API:**
    Abre tu navegador web y ve a `http://localhost:8001/docs`. Deberías ver la documentación interactiva de la API generada automáticamente por FastAPI. ¡Desde aquí puedes probar los diferentes endpoints!

9.  **Ejecutar el Worker Celery (Opcional, si necesitas probar el entrenamiento):**
    Abre *otra* terminal, activa el entorno virtual (`source venv/bin/activate`) y ejecuta:
    ```bash
    celery -A app.celery_worker.celery_app worker --loglevel=info
    ```
    Este worker escuchará tareas (como el entrenamiento) que la API envíe a Redis.

¡Listo! Ahora tienes la API Rayuela funcionando en tu máquina local.

## Despliegue (Producción en Google Cloud Run)

Llevar la aplicación a producción significa hacerla accesible en internet de forma fiable y escalable. Usaremos Google Cloud Platform (GCP), específicamente Cloud Run, que es excelente para empezar por su modelo de pago por uso y facilidad de manejo.

**Requisitos Previos:**

1.  **Cuenta de Google Cloud Platform (GCP):** Necesitas una cuenta y un proyecto creado. [Crear cuenta GCP](https://cloud.google.com/). Ten en cuenta que necesitarás añadir información de facturación, aunque GCP ofrece un nivel gratuito generoso.
2.  **Google Cloud SDK (`gcloud`):** La herramienta de línea de comandos para interactuar con GCP. [Instalar gcloud CLI](https://cloud.google.com/sdk/docs/install). Después de instalar, configúrala con tu cuenta: `gcloud auth login` y `gcloud config set project TU_ID_DE_PROYECTO`.
3.  **Habilitar APIs de GCP:** En tu proyecto GCP, asegúrate de que las siguientes APIs estén habilitadas (puedes hacerlo desde la consola web de GCP):
    *   Cloud Run API
    *   Cloud Build API
    *   Artifact Registry API (o Container Registry API)
    *   Cloud SQL Admin API
    *   Secret Manager API
    *   Cloud Storage API
    *   Redis API (si usas Cloud Memorystore)

**Pasos:**

1.  **Configurar Infraestructura en GCP:**
    Necesitamos crear los servicios equivalentes a nuestro PostgreSQL, Redis y almacenamiento local.
    *   **Base de Datos (Cloud SQL):**
        *   Crea una instancia de PostgreSQL en Cloud SQL. Elige una región cercana a tus usuarios.
        *   Configura un usuario y una contraseña seguros. ¡Anótalos!
        *   **Importante:** Configura la instancia para usar una **IP privada** dentro de una red VPC para mayor seguridad. No la expongas directamente a internet.
        *   Crea la base de datos dentro de la instancia (ej: `rayuela_prod`).
    *   **Caché (Cloud Memorystore for Redis):**
        *   Crea una instancia de Redis en Memorystore.
        *   Elige la misma región y red VPC que tu instancia de Cloud SQL.
        *   Anota la **IP privada** y el puerto (normalmente 6379).
    *   **Almacenamiento de Modelos (Cloud Storage):**
        *   Crea un Bucket de Cloud Storage. Elige un nombre único globalmente.
        *   Selecciona una ubicación (región) y clase de almacenamiento (Standard es un buen comienzo).
    *   **Gestión de Secretos (Secret Manager):**
        *   ¡No pongas contraseñas ni API keys directamente en variables de entorno en producción! Usa Secret Manager.
        *   Crea secretos para:
            *   Contraseña de la base de datos.
            *   La `SECRET_KEY` de FastAPI/JWT.
            *   Cualquier otra clave de API externa que uses.
        *   Anota los nombres de estos secretos (ej: `projects/TU_PROYECTO/secrets/db_password/versions/latest`).

2.  **Configurar Acceso a Red (VPC Network y Serverless VPC Access):**
    *   Como Cloud SQL y Memorystore están en una red privada (VPC), Cloud Run necesita una forma de conectarse a ellos.
    *   Crea un **Conector de Acceso VPC Sin Servidor** (Serverless VPC Access Connector) en la misma región y red VPC que tu base de datos y caché.

3.  **Preparar la Aplicación para Producción:**
    *   **Dockerfile:** Asegúrate de que el `Dockerfile` en la raíz del proyecto esté optimizado para producción. Debería:
        *   Usar una imagen base de Python ligera (ej: `python:3.10-slim`).
        *   Copiar solo los archivos necesarios.
        *   Instalar dependencias desde `requirements.txt`.
        *   Exponer el puerto correcto (ej: 8000).
        *   Usar un servidor ASGI de producción como `gunicorn` con workers `uvicorn` (no el `--reload` de desarrollo). El `CMD` o `ENTRYPOINT` podría ser algo como: `gunicorn -w 4 -k uvicorn.workers.UvicornWorker app.main:app --bind 0.0.0.0:$PORT` (Cloud Run inyecta la variable `$PORT`).
    *   **Configuración (`.env`):** NO subas tu archivo `.env` con secretos a tu repositorio Git. La configuración en Cloud Run se hará a través de variables de entorno y secretos montados.

4.  **Construir y Subir la Imagen Docker:**
    Usaremos Cloud Build para construir la imagen Docker y Artifact Registry (o Container Registry) para almacenarla.
    ```bash
    gcloud builds submit --tag gcr.io/TU_ID_DE_PROYECTO/rayuela:v1.0.0
    ```
    *   Reemplaza `TU_ID_DE_PROYECTO` con el ID de tu proyecto GCP.
    *   `rayuela` es el nombre que le damos a nuestra imagen.
    *   `v1.0.0` es una etiqueta de versión (cámbiala para futuras actualizaciones).
    *   (Si usas Artifact Registry, la URL será ligeramente diferente, ej: `us-central1-docker.pkg.dev/TU_PROYECTO/mi-repo/rayuela:v1.0.0`).

5.  **Desplegar en Cloud Run:**
    Ahora creamos el servicio Cloud Run usando la imagen que acabamos de subir.
    ```bash
    gcloud run deploy rayuela-prod \
        --image gcr.io/TU_ID_DE_PROYECTO/rayuela:v1.0.0 \
        --platform managed \
        --region TU_REGION \ # Ej: us-central1
        --allow-unauthenticated \ # O --no-allow-unauthenticated si quieres controlar el acceso con IAM
        --vpc-connector TU_NOMBRE_DEL_CONECTOR_VPC \
        --set-env-vars="MODEL_STORAGE_TYPE=gcs,GCS_BUCKET_NAME=TU_BUCKET_GCS" \
        --set-env-vars="DATABASE_URL=postgresql+asyncpg://TU_USUARIO_DB@//cloudsql/TU_PROYECTO:TU_REGION:TU_INSTANCIA_SQL/TU_BASE_DE_DATOS" \ # ¡Ojo! El formato es especial para Cloud SQL Proxy
        --set-env-vars="REDIS_URL=redis://IP_PRIVADA_REDIS:6379/0" \
        --set-secrets="DB_PASSWORD=projects/TU_PROYECTO/secrets/db_password:latest" \
        --set-secrets="SECRET_KEY=projects/TU_PROYECTO/secrets/fastapi_secret_key:latest" \
        # Añade aquí el resto de variables de entorno y secretos necesarios
        --add-cloudsql-instances TU_PROYECTO:TU_REGION:TU_INSTANCIA_SQL # Vincula la instancia SQL
    ```
    *   **Reemplaza** todos los `TU_*` con tus valores reales.
    *   `rayuela-prod`: Nombre de tu servicio en Cloud Run.
    *   `--platform managed`: Usa la infraestructura gestionada de Google.
    *   `--region`: La misma región donde creaste tus otros servicios (SQL, Redis, VPC Connector).
    *   `--allow-unauthenticated`: Permite el acceso público. Para una API B2B, probablemente querrás gestionar el acceso de forma más segura más adelante (ej: API Gateway, IAP).
    *   `--vpc-connector`: El nombre del conector VPC que creaste.
    *   `--set-env-vars`: Define variables de entorno. **No pongas secretos aquí.** Fíjate en el formato especial para `DATABASE_URL` cuando usas el proxy de Cloud SQL integrado (`//cloudsql/...`). Necesitarás añadir el usuario de la BD aquí, pero la contraseña irá como secreto.
    *   `--set-secrets`: Monta los secretos de Secret Manager como variables de entorno (o como archivos si lo prefieres). El formato es `VARIABLE_DE_ENTORNO=nombre_del_secreto:version`.
    *   `--add-cloudsql-instances`: Habilita el proxy interno de Cloud Run para conectarse de forma segura a Cloud SQL.

6.  **Aplicar Migraciones en Producción:**
    Necesitas ejecutar `alembic upgrade head` contra la base de datos de producción. Hay varias formas de hacerlo:
    *   **Opción 1 (Temporal):** Conectarte a la instancia de Cloud SQL usando el [Cloud SQL Auth Proxy](https://cloud.google.com/sql/docs/postgres/connect-auth-proxy) desde tu máquina local y ejecutar Alembic apuntando a `localhost:PUERTO_DEL_PROXY`.
    *   **Opción 2 (Mejor):** Crear un "Job" de Cloud Run que use la misma imagen Docker, se conecte a la base de datos y ejecute el comando `alembic upgrade head` y luego termine. Esto es más automatizable.
    *   **Opción 3 (CI/CD):** Integrar la ejecución de migraciones en tu pipeline de despliegue continuo.

7.  **Configurar el Worker Celery (Producción):**
    El worker Celery necesita ejecutarse continuamente en segundo plano. Cloud Run no es ideal para procesos de larga duración que *siempre* deben estar activos (aunque puede funcionar para tareas bajo demanda con configuraciones específicas de CPU siempre asignada). Opciones comunes:
    *   **Google Compute Engine (GCE):** Una máquina virtual tradicional donde instalas y ejecutas Celery. Más control, pero más gestión.
    *   **Google Kubernetes Engine (GKE):** Más complejo, pero muy potente para orquestar contenedores, incluyendo workers.
    *   **Cloud Run (con CPU siempre asignada):** Puede funcionar si los entrenamientos no son extremadamente largos y puedes permitirte el costo de tener la instancia siempre activa. Configura el servicio de Cloud Run para el worker para que *no* reciba tráfico HTTP y ejecute el comando de Celery. Asegúrate de que tenga acceso a la VPC para Redis.

8.  **¡Probar!**
    Cloud Run te dará una URL pública para tu servicio API. Usa una herramienta como `curl` o Postman para probar los endpoints con tus API Keys de producción.

¡Felicidades! Has desplegado la API en un entorno escalable en la nube.

## Configuración del Servidor

La aplicación ahora permite configurar el host y puerto del servidor a través de variables de entorno, lo que facilita el despliegue en diferentes entornos.

### Variables de Entorno para la Configuración del Servidor

| Variable   | Descripción                                | Valor por defecto | Rango válido        |
|------------|--------------------------------------------|--------------------|---------------------|
| `API_HOST` | Dirección IP en la que escucha el servidor | `0.0.0.0`          | Cualquier IP válida |
| `API_PORT` | Puerto en el que escucha el servidor       | `8001`             | 1024-65535          |

### Configuración en Desarrollo

Para configurar el servidor en un entorno de desarrollo, puedes:

1. Establecer las variables de entorno directamente:
   ```bash
   # En Linux/Mac
   export API_PORT=8002
   python main.py

   # En Windows (PowerShell)
   $env:API_PORT=8002; python main.py

   # En Windows (CMD)
   set API_PORT=8002
   python main.py
   ```

2. O añadir estas variables a tu archivo `.env`:
   ```
   API_HOST=0.0.0.0
   API_PORT=8002
   ```

### Configuración en Producción

En un entorno de producción (como Cloud Run), estas variables se configuran como parte del despliegue:

```bash
gcloud run deploy rayuela-api-prod \
  --set-env-vars="API_HOST=0.0.0.0,API_PORT=8080" \
  # ... otras opciones de despliegue
```

### Manejo de Puertos Ocupados

La aplicación ahora incluye un mecanismo para manejar puertos ocupados:

1. Si el puerto configurado está ocupado, la aplicación intentará encontrar un puerto disponible automáticamente.
2. La búsqueda comenzará desde el puerto configurado + 1 y continuará hasta encontrar un puerto disponible o alcanzar el número máximo de intentos (10 por defecto).
3. Si se encuentra un puerto disponible, la aplicación se iniciará en ese puerto y mostrará un mensaje informativo.
4. Si no se encuentra un puerto disponible, la aplicación mostrará un error y se detendrá.

## Configuración (Variables de Entorno)

La aplicación se configura principalmente a través de variables de entorno. Aquí están las más importantes (consulta `.env.example` para la lista completa):

| Variable                    | Descripción                                                                 | Ejemplo (Local)                                                    | Ejemplo (Producción - Cloud Run)                                                                 |
| :-------------------------- | :-------------------------------------------------------------------------- | :----------------------------------------------------------------- | :----------------------------------------------------------------------------------------------- |
| `ENVIRONMENT`               | Indica el entorno (development, staging, production)                        | `development`                                                      | `production`                                                                                     |
| `DATABASE_URL`              | URL de conexión a la base de datos PostgreSQL (usando `asyncpg`)            | `postgresql+asyncpg://user:pass@localhost:5432/db_dev`             | `postgresql+asyncpg://user@//cloudsql/proj:reg:inst/db_prod` (Password via Secret)               |
| `REDIS_URL`                 | URL de conexión a Redis                                                     | `redis://localhost:6379/0`                                         | `redis://IP_PRIVADA_REDIS:6379/0`                                                                |
| `SECRET_KEY`                | Clave secreta para firmar JWTs y otros datos seguros                        | `una_clave_larga_y_aleatoria_muy_secreta`                          | (Montado desde Secret Manager)                                                                   |
| `ALGORITHM`                 | Algoritmo de hashing para JWT                                               | `HS256`                                                            | `HS256`                                                                                          |
| `ACCESS_TOKEN_EXPIRE_MINUTES` | Tiempo de vida de los tokens de acceso JWT en minutos                       | `30`                                                               | `60`                                                                                             |
| `API_KEY_HEADER`            | Nombre de la cabecera HTTP para la API Key del cliente                      | `X-API-Key`                                                        | `X-API-Key`                                                                                      |
| `MODEL_STORAGE_TYPE`        | Dónde guardar los modelos (`local` o `gcs`)                                 | `local`                                                            | `gcs`                                                                                            |
| `LOCAL_MODEL_STORAGE_PATH`  | Ruta local para guardar modelos (si `MODEL_STORAGE_TYPE=local`)             | `./local_models`                                                   | N/A                                                                                              |
| `GCS_BUCKET_NAME`           | Nombre del bucket de Google Cloud Storage (si `MODEL_STORAGE_TYPE=gcs`)     | N/A                                                                | `tu-bucket-de-modelos-rayuela`                                                                   |
| `GCS_PROJECT_ID`            | ID del proyecto GCP (opcional si la autenticación se infiere del entorno) | N/A                                                                | (Generalmente inferido en Cloud Run)                                                             |
| `CELERY_BROKER_URL`         | URL de Redis para que Celery lo use como intermediario de mensajes          | `redis://localhost:6379/1` (Usar DB diferente a la caché)          | `redis://IP_PRIVADA_REDIS:6379/1`                                                                |
| `CELERY_RESULT_BACKEND`     | URL de Redis para que Celery guarde los resultados de las tareas            | `redis://localhost:6379/2` (Usar DB diferente)                     | `redis://IP_PRIVADA_REDIS:6379/2`                                                                |
| `LOG_LEVEL`                 | Nivel de detalle de los logs (DEBUG, INFO, WARNING, ERROR)                  | `DEBUG`                                                            | `INFO`                                                                                           |

**Importante:** Nunca guardes secretos (contraseñas, API keys, `SECRET_KEY`) directamente en tu código o en archivos que se suban a Git. Usa el archivo `.env` **solo para desarrollo local** (y asegúrate de que `.env` esté en tu `.gitignore`) y **Secret Manager** en producción.

## Documentación de la API

La documentación interactiva de la API (generada con Swagger UI) está disponible automáticamente cuando la API está en ejecución.

*   **Localmente:** `http://localhost:8001/docs`
*   **En Producción:** `https://TU_URL_DE_CLOUD_RUN/docs`

También hay una documentación alternativa (ReDoc) en `/redoc`.

### Documentación Adicional

*   [Códigos de Error](docs/ERROR_CODES.md): Documentación detallada de los códigos de error estandarizados de la API.
*   [Guía de Inicio Rápido](QUICKSTART.md): Guía concisa para comenzar a utilizar la API rápidamente.

## Pruebas

El proyecto incluye un conjunto completo de pruebas unitarias y de integración para garantizar la calidad y robustez del código.

### Ejecutar Pruebas

Para ejecutar las pruebas, asegúrate de tener instaladas las dependencias de desarrollo:

```bash
pip install -r requirements-dev.txt
```

Luego, puedes ejecutar las pruebas con pytest:

```bash
python -m pytest
```

### Cobertura de Pruebas

Para ejecutar las pruebas con cobertura y generar informes, utiliza el script proporcionado:

```bash
# En Linux/Mac
chmod +x scripts/run_coverage_report.sh
./scripts/run_coverage_report.sh

# En Windows
python scripts/run_tests_with_coverage.py --html
```

Esto generará un informe de cobertura en HTML en el directorio `coverage_reports/html/`.

### Estructura de Pruebas

Las pruebas están organizadas siguiendo la estructura del proyecto:

- `tests/unit/`: Pruebas unitarias para componentes individuales
  - `tests/unit/api/`: Pruebas para endpoints de la API
  - `tests/unit/ml_pipeline/`: Pruebas para componentes del pipeline de ML
  - `tests/unit/services/`: Pruebas para la capa de servicios
- `tests/integration/`: Pruebas de integración entre componentes
- `tests/conftest.py`: Configuración y fixtures compartidos para las pruebas

### Buenas Prácticas de Pruebas

- **Pruebas Unitarias**: Cada componente debe tener pruebas unitarias que verifiquen su comportamiento aislado.
- **Mocking**: Utiliza mocks para aislar el componente que estás probando de sus dependencias.
- **Pruebas de Caminos Condicionales**: Asegúrate de probar todos los caminos condicionales en el código.
- **Pruebas de Manejo de Errores**: Verifica que los errores se manejen correctamente.
- **Pruebas de Límites**: Prueba los casos límite y valores extremos.

## Próximos Pasos y Evolución

Rayuela es una base sólida, pero siempre hay espacio para mejorar y crecer. Aquí algunas ideas:

1.  **Mejoras en los Modelos ML:**
    *   **Experimentación:** Probar algoritmos más avanzados (Factorization Machines, Deep Learning si el presupuesto lo permite).
    *   **Evaluación Rigurosa:** Implementar métricas offline (Precision@K, Recall@K, NDCG) y online (A/B testing) más robustas.
    *   **Cold Start:** Mejorar las recomendaciones para usuarios nuevos o productos nuevos (ej: usar más metadatos de contenido, recomendaciones populares).
    *   **Actualización Dinámica:** Explorar formas de actualizar modelos o recomendaciones con datos más recientes sin reentrenamientos completos constantes (near real-time).
2.  **Escalabilidad y Operaciones:**
    *   **Base de Datos:** Optimizar consultas, revisar índices, considerar estrategias de archivado de datos antiguos si las tablas crecen mucho. Evaluar lectura de réplicas si es necesario.
    *   **Workers Celery:** Monitorizar el rendimiento de los workers. Considerar escalar horizontalmente (más workers) si la cola de tareas crece. Explorar alternativas como Cloud Tasks si Celery se vuelve complejo de gestionar.
    *   **Infraestructura como Código (IaC):** Usar herramientas como Terraform o Pulumi para definir y gestionar la infraestructura de GCP (Cloud SQL, Redis, GCS, Cloud Run) mediante código, lo que hace las actualizaciones y la replicación de entornos mucho más fácil y fiable.
    *   **CI/CD (Integración Continua / Despliegue Continuo):** Automatizar las pruebas, construcción de imágenes y despliegues usando Cloud Build, GitHub Actions, GitLab CI u otros.
3.  **Características de la Plataforma SaaS:**
    *   **Panel de Administración del Cliente:** Construir un frontend (React, Vue, Angular) donde los clientes puedan registrarse, gestionar sus API Keys, ver su uso, configurar opciones básicas de recomendación y ver analíticas.
    *   **Analíticas Avanzadas:** Ofrecer a los clientes dashboards con el rendimiento de las recomendaciones (CTR, tasas de conversión atribuidas).
    *   **Monitorización y Alertas:** Configurar monitorización detallada (uso de CPU/memoria, latencia de API, errores, tamaño de colas Celery) usando Cloud Monitoring y alertas para problemas críticos.
    *   **Logging Centralizado:** Asegurar que todos los logs (API, workers) se envíen a Cloud Logging para facilitar la depuración.
4.  **Seguridad:**
    *   **Auditoría de Seguridad:** Realizar revisiones periódicas de seguridad.
    *   **Gestión de Permisos (IAM):** Asegurar que los roles y permisos en GCP sean lo más restrictivos posible (principio de mínimo privilegio).
    *   **Protección de API:** Considerar usar Google Cloud Armor o un API Gateway para protección contra DDoS, WAF (Web Application Firewall) y gestión de acceso más granular.
5.  **Experiencia del Desarrollador (DX):**
    *   **SDKs:** Crear librerías cliente ligeras en lenguajes populares (Python, JavaScript) para facilitar la integración con la API de Rayuela.
    *   **Documentación Mejorada:** Añadir tutoriales, guías de inicio rápido y ejemplos de código más elaborados.
6.  **Mejoras en las Pruebas:**
    *   **Aumentar Cobertura:** Incrementar la cobertura de pruebas al 90% en todo el código.
    *   **Pruebas de Rendimiento:** Implementar pruebas de carga y rendimiento para identificar cuellos de botella.
    *   **Pruebas de Integración Continua:** Configurar pruebas automatizadas en el pipeline de CI/CD.
    *   **Pruebas de Regresión:** Desarrollar un conjunto de pruebas de regresión para evitar la reintroducción de errores.

## Contribuciones

Este es un software propietario desarrollado por [Nombre de tu Empresa o Tu Nombre]. Como tal, no se aceptan contribuciones externas al código fuente. El acceso al código está restringido al equipo de desarrollo interno.

## Licencia

El código fuente de Rayuela es **propiedad intelectual** de [Nombre de tu Empresa o Tu Nombre] y está protegido por las leyes de derechos de autor. **No es software de código abierto.**

**Queda estrictamente prohibida la copia, distribución, modificación, ingeniería inversa o cualquier otro uso no autorizado explícitamente por [Nombre de tu Empresa o Tu Nombre] del código fuente o de cualquier parte del mismo.**

El uso de la API como servicio se rige por los Términos y Condiciones de Servicio acordados al contratar la suscripción a Rayuela.

## Entrenamiento Escalable con Cloud Run Jobs 🏃‍♂️☁️

A partir de la versión `vX.Y.Z` el proceso de entrenamiento puede ejecutarse como **Cloud Run Jobs** en lugar de Workers Celery tradicionales. Esto permite pagar **solo** por el tiempo de cómputo realmente utilizado durante cada entrenamiento y reduce el número de instancias de backend siempre encendidas.

### Cómo habilitar la funcionalidad

1. Establece la variable de entorno `USE_CLOUD_RUN_JOBS` en `true` **antes** de iniciar la aplicación FastAPI (o en tu _Deployment YAML_ / Cloud Run Service):

```bash
export USE_CLOUD_RUN_JOBS=true  # <– activa modo Cloud Run Jobs
```

2. Cuando el endpoint `POST /api/v1/pipeline/train` se invoque, la API:
   * Creará el registro `TrainingJob` en la base de datos (estado `PENDING`).
   * Lanzará un **Cloud Run Job** con la misma imagen del backend, pasando los parámetros de entrenamiento y el `job_id`.
   * Actualizará el `task_id` del registro con el **nombre del Job** y marcará su estado como `RUNNING`.

3. El Job ejecutará el comando:

```bash
python -m src.workers.celery_tasks train_model_for_job --account_id <ACCOUNT_ID> --job_id <JOB_ID>
```

Una vez finalizado, el propio proceso actualizará el estado (`COMPLETED`/`FAILED`) y las métricas.

### Variables de entorno adicionales (opcional)

| Variable                        | Descripción                                                           | Valor por defecto |
|---------------------------------|-----------------------------------------------------------------------|-------------------|
| `CLOUD_RUN_REGION`             | Región donde se ejecutará el Job                                       | `us-central1`     |
| `PROJECT_ID` / `GCP_PROJECT`   | ID del proyecto de GCP                                                 | —                 |
| `BUILD_ID`                     | Tag de la imagen; útil si usas Cloud Build                             | `latest`          |
| `TRAINING_JOB_IMAGE`           | URI completo de la imagen a usar (anula REGION/PROJECT/BUILD)         | Dinámico*         |
| `TRAINING_JOB_MEMORY`          | Memoria por tarea (`gcloud run jobs create --memory`)                  | `8Gi`             |
| `TRAINING_JOB_CPU`             | CPUs por tarea (`--cpu`)                                               | `4`               |
| `TRAINING_JOB_TIMEOUT`         | Tiempo máximo de ejecución en segundos (`--task-timeout`)             | `3600`            |
| `CLOUD_RUN_VPC_CONNECTOR`      | Conector VPC si necesitas acceso a recursos privados                   | `rayuela-vpc-connector` |
| `CLOUD_RUN_SA`                 | Service Account que ejecuta el Job                                     | `rayuela-backend-sa@<PROJECT_ID>.iam.gserviceaccount.com` |

\* Si no defines `TRAINING_JOB_IMAGE`, se compone automáticamente como:
```
<REGION>-docker.pkg.dev/<PROJECT_ID>/rayuela-repo/rayuela-backend:<BUILD_ID>
```

### Modo _dry-run_ (desarrollo local)

Para ver los comandos `gcloud` generados sin realmente ejecutarlos:

```python
from src.services.cloud_run_launcher import launch_training_job
launch_training_job(account_id=1, job_id=123, parameters={}, dry_run=True)
```

Esto imprimirá los comandos en los logs y devolverá el nombre que se usaría para el Job.

### Compatibilidad retro‐compatible

Si **no** estableces `USE_CLOUD_RUN_JOBS`, el flujo seguirá utilizando Celery como antes, sin cambios en tu entorno local de pruebas.

---
