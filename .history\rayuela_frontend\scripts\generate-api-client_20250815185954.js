#!/usr/bin/env node

/**
 * Script para generar el cliente API de Rayuela con configuración de seguridad.
 * 
 * SEGURIDAD: Este script permite elegir entre la versión pública (por defecto)
 * y la versión privada del OpenAPI para proteger la propiedad intelectual.
 * 
 * Uso:
 *   npm run generate-api          # Usa versión pública (recomendado)
 *   npm run generate-api:private  # Usa versión privada (solo desarrollo)
 */

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuración
const BACKEND_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8001';
const USE_PRIVATE = process.argv.includes('--private') || process.env.ORVAL_USE_PRIVATE_API === 'true';

// Colores para logs
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logHeader(message) {
  log(`\n🚀 ${message}`, 'bright');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

function logSecurity(message) {
  log(`🛡️  ${message}`, 'magenta');
}

async function checkBackendHealth() {
  logInfo('Verificando que el backend esté disponible...');
  
  try {
    const result = execSync(`curl -s -o /dev/null -w "%{http_code}" ${BACKEND_URL}/health`, {
      encoding: 'utf8',
      timeout: 5000
    });
    
    if (result.trim() === '200') {
      logSuccess('Backend disponible');
      return true;
    } else {
      logWarning(`Backend respondió con código: ${result}`);
      return false;
    }
  } catch (error) {
    logError('Backend no disponible. Asegúrate de que esté ejecutándose en ' + BACKEND_URL);
    logInfo('Para iniciar el backend: cd rayuela_backend && python -m uvicorn main:app --host 0.0.0.0 --port 8001');
    return false;
  }
}

async function generateApiClient() {
  logHeader('Generando Cliente API de Rayuela');
  
  // Mostrar configuración de seguridad
  if (USE_PRIVATE) {
    logSecurity('MODO PRIVADO: Generando con especificación completa (solo para desarrollo)');
    logWarning('Este modo expone detalles internos de ML. NO usar en producción.');
  } else {
    logSecurity('MODO PÚBLICO: Generando con especificación minimalista (recomendado)');
    logInfo('Este modo protege la propiedad intelectual de Rayuela.');
  }
  
  // Verificar backend
  const backendAvailable = await checkBackendHealth();
  if (!backendAvailable) {
    logError('No se puede generar el cliente sin el backend disponible');
    process.exit(1);
  }
  
  // Configurar variables de entorno para orval
  const env = {
    ...process.env,
    ORVAL_USE_PRIVATE_API: USE_PRIVATE ? 'true' : 'false'
  };
  
  try {
    logInfo('Ejecutando orval...');
    
    // Ejecutar orval con la configuración apropiada
    execSync('npx orval', {
      stdio: 'inherit',
      env: env,
      cwd: process.cwd()
    });
    
    logSuccess('Cliente API generado exitosamente');
    
    // Verificar que el archivo se generó
    const generatedFile = path.join(process.cwd(), 'src/lib/generated/rayuelaAPI.ts');
    if (fs.existsSync(generatedFile)) {
      const stats = fs.statSync(generatedFile);
      logInfo(`Archivo generado: ${generatedFile} (${Math.round(stats.size / 1024)}KB)`);
      
      // Mostrar información sobre la versión generada
      const content = fs.readFileSync(generatedFile, 'utf8');
      const hasPrivateSchemas = content.includes('ModelMetadataResponse') || 
                               content.includes('performance_metrics') ||
                               content.includes('collaborative');
      
      if (hasPrivateSchemas && !USE_PRIVATE) {
        logWarning('ADVERTENCIA: El cliente contiene esquemas privados. Verifica la configuración.');
      } else if (!hasPrivateSchemas && USE_PRIVATE) {
        logWarning('ADVERTENCIA: El cliente no contiene esquemas privados esperados.');
      } else {
        logSuccess(`Cliente generado correctamente en modo ${USE_PRIVATE ? 'PRIVADO' : 'PÚBLICO'}`);
      }
    } else {
      logError('El archivo del cliente no se generó correctamente');
      process.exit(1);
    }
    
  } catch (error) {
    logError('Error al generar el cliente API:');
    console.error(error.message);
    process.exit(1);
  }
}

async function main() {
  try {
    await generateApiClient();
    
    logHeader('Generación Completada');
    logSuccess('El cliente API de Rayuela está listo para usar');
    
    if (USE_PRIVATE) {
      logWarning('RECORDATORIO: Estás usando la versión privada del API');
      logWarning('Cambia a la versión pública antes de desplegar a producción');
      logInfo('Para usar la versión pública: npm run generate-api');
    } else {
      logInfo('Estás usando la versión pública (recomendada para producción)');
      logInfo('Para desarrollo avanzado: npm run generate-api:private');
    }
    
  } catch (error) {
    logError('Error durante la generación:');
    console.error(error);
    process.exit(1);
  }
}

// Ejecutar si es llamado directamente
if (require.main === module) {
  main();
}

module.exports = { generateApiClient, checkBackendHealth };
