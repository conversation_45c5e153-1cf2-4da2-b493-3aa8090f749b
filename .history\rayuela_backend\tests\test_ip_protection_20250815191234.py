"""
Tests para verificar la protección de propiedad intelectual en OpenAPI.

Este módulo contiene tests que verifican que:
1. La versión pública del OpenAPI no expone información sensible
2. La versión privada requiere autenticación
3. Los esquemas públicos no contienen términos técnicos de ML
4. Los mapeos de ofuscación funcionan correctamente
"""

import pytest
import json
import requests
from fastapi.testclient import TestClient
from main import app

client = TestClient(app)

# Headers válidos para el middleware TrustedHost
VALID_HEADERS = {"host": "localhost"}

# Términos sensibles que NO deben aparecer en la versión pública
SENSITIVE_TERMS = [
    "collaborative", "content-based", "hybrid",  # Tipos de modelo técnicos
    "precision", "recall", "ndcg", "map_score",  # Métricas de ML
    "catalog_coverage", "diversity", "novelty", "serendipity",  # Métricas avanzadas
    "balanced", "maximize_engagement", "discover_diverse", "promote_new_arrivals",  # Estrategias técnicas
    "collab_weight", "content_weight", "diversity_lambda",  # Parámetros internos
    "model_type", "strategy", "performance_metrics"  # Campos técnicos
]

# Términos públicos que SÍ deben aparecer en la versión pública
PUBLIC_TERMS = [
    "conversion_optimization", "user_engagement", "discovery_mode", "catalog_promotion",  # Objetivos públicos
    "standard", "premium", "enterprise",  # Variantes de modelo públicas
    "recommendation_goal", "model_variant",  # Campos públicos
    "PublicRecommendationGoal", "PublicModelVariant", "PublicExplanationLevel"  # Esquemas públicos
]

class TestIPProtection:
    """Tests para verificar la protección de propiedad intelectual."""
    
    def test_public_openapi_accessible_without_auth(self):
        """Verificar que el endpoint público es accesible sin autenticación."""
        response = client.get("/api/v1/public-openapi.json", headers=VALID_HEADERS)

        # Debug: imprimir respuesta si hay error
        if response.status_code != 200:
            print(f"Status: {response.status_code}")
            print(f"Response: {response.text}")

        assert response.status_code == 200
        assert response.headers["content-type"] == "application/json"

        openapi_spec = response.json()
        assert openapi_spec["openapi"] == "3.1.0"
        assert "Rayuela Public API" in openapi_spec["info"]["title"]
    
    def test_private_openapi_requires_auth(self):
        """Verificar que el endpoint privado requiere autenticación."""
        try:
            response = client.get("/api/v1/openapi.json", headers=VALID_HEADERS)
            # Debe retornar 401 o 403 sin autenticación
            assert response.status_code in [401, 403]
        except Exception as e:
            # Si lanza excepción, verificar que sea por falta de autenticación
            assert "401" in str(e) or "API Key is required" in str(e)
    
    def test_public_openapi_no_sensitive_terms(self):
        """Verificar que la versión pública no contiene términos sensibles."""
        response = client.get("/api/v1/public-openapi.json", headers=VALID_HEADERS)
        assert response.status_code == 200
        
        openapi_content = json.dumps(response.json()).lower()
        
        for sensitive_term in SENSITIVE_TERMS:
            assert sensitive_term.lower() not in openapi_content, (
                f"Término sensible '{sensitive_term}' encontrado en OpenAPI público. "
                f"Esto expone propiedad intelectual."
            )
    
    def test_public_openapi_contains_public_terms(self):
        """Verificar que la versión pública contiene términos públicos apropiados."""
        response = client.get("/api/v1/public-openapi.json", headers=VALID_HEADERS)
        assert response.status_code == 200
        
        openapi_content = json.dumps(response.json()).lower()
        
        for public_term in PUBLIC_TERMS:
            assert public_term.lower() in openapi_content, (
                f"Término público '{public_term}' no encontrado en OpenAPI público. "
                f"La ofuscación puede estar incompleta."
            )
    
    def test_public_schemas_structure(self):
        """Verificar la estructura de los esquemas públicos."""
        response = client.get("/api/v1/public-openapi.json", headers=VALID_HEADERS)
        assert response.status_code == 200
        
        openapi_spec = response.json()
        schemas = openapi_spec.get("components", {}).get("schemas", {})
        
        # Verificar que existen los esquemas públicos
        required_public_schemas = [
            "PublicRecommendationGoal",
            "PublicModelVariant", 
            "PublicExplanationLevel",
            "PublicRecommendationQueryRequest",
            "PublicRecommendationQueryExternalRequest"
        ]
        
        for schema_name in required_public_schemas:
            assert schema_name in schemas, f"Esquema público '{schema_name}' no encontrado"
    
    def test_public_recommendation_goal_values(self):
        """Verificar que PublicRecommendationGoal contiene valores ofuscados."""
        response = client.get("/api/v1/public-openapi.json", headers=VALID_HEADERS)
        assert response.status_code == 200
        
        openapi_spec = response.json()
        schemas = openapi_spec["components"]["schemas"]
        
        goal_schema = schemas["PublicRecommendationGoal"]
        allowed_values = goal_schema["enum"]
        
        expected_values = ["conversion_optimization", "user_engagement", "discovery_mode", "catalog_promotion"]
        assert set(allowed_values) == set(expected_values), (
            f"Valores de PublicRecommendationGoal incorrectos. "
            f"Esperado: {expected_values}, Encontrado: {allowed_values}"
        )
    
    def test_public_model_variant_values(self):
        """Verificar que PublicModelVariant contiene valores orientados al plan."""
        response = client.get("/api/v1/public-openapi.json", headers=VALID_HEADERS)
        assert response.status_code == 200
        
        openapi_spec = response.json()
        schemas = openapi_spec["components"]["schemas"]
        
        variant_schema = schemas["PublicModelVariant"]
        allowed_values = variant_schema["enum"]
        
        expected_values = ["standard", "premium", "enterprise"]
        assert set(allowed_values) == set(expected_values), (
            f"Valores de PublicModelVariant incorrectos. "
            f"Esperado: {expected_values}, Encontrado: {allowed_values}"
        )
    
    def test_public_endpoints_filtered(self):
        """Verificar que solo los endpoints apropiados están en la versión pública."""
        response = client.get("/api/v1/public-openapi.json", headers=VALID_HEADERS)
        assert response.status_code == 200
        
        openapi_spec = response.json()
        paths = openapi_spec.get("paths", {})
        
        # Endpoints que deben estar presentes
        required_endpoints = [
            "/health",
            "/api/v1/auth/register",
            "/api/v1/auth/token",
            "/api/v1/auth/logout",
            "/api/v1/accounts/current",
            "/api/v1/accounts/usage",
            "/api/v1/end-users/",
            "/api/v1/products/",
            "/api/v1/recommendations/personalized/query"
        ]
        
        for endpoint in required_endpoints:
            assert endpoint in paths, f"Endpoint requerido '{endpoint}' no encontrado en versión pública"
        
        # Verificar que no hay endpoints administrativos sensibles
        sensitive_endpoints = [
            "/api/v1/admin/",
            "/api/v1/models/",
            "/api/v1/analytics/detailed",
            "/api/v1/pipeline/internal"
        ]
        
        for endpoint in sensitive_endpoints:
            # Buscar endpoints que empiecen con estos patrones
            matching_endpoints = [path for path in paths.keys() if path.startswith(endpoint)]
            assert len(matching_endpoints) == 0, (
                f"Endpoint sensible '{endpoint}' encontrado en versión pública: {matching_endpoints}"
            )
    
    def test_public_openapi_security_description(self):
        """Verificar que la documentación menciona la protección de IP."""
        response = client.get("/api/v1/public-openapi.json", headers=VALID_HEADERS)
        assert response.status_code == 200
        
        openapi_spec = response.json()
        description = openapi_spec["info"]["description"].lower()
        
        # Verificar que la descripción es apropiada para versión pública
        assert "public api" in description or "pública" in description
        assert "recommendation" in description or "recomendación" in description
        
        # No debe mencionar detalles técnicos internos
        technical_terms = ["machine learning", "ml", "algorithm", "modelo interno", "collaborative filtering"]
        for term in technical_terms:
            assert term not in description, f"Término técnico '{term}' encontrado en descripción pública"


class TestConversionMapping:
    """Tests para verificar que los mapeos de conversión funcionan correctamente."""
    
    def test_goal_mapping_coverage(self):
        """Verificar que todos los objetivos públicos tienen mapeo interno."""
        from src.db.schemas.public_recommendation import PUBLIC_TO_INTERNAL_GOAL_MAPPING
        
        public_goals = ["conversion_optimization", "user_engagement", "discovery_mode", "catalog_promotion"]
        
        for goal in public_goals:
            assert goal in PUBLIC_TO_INTERNAL_GOAL_MAPPING, (
                f"Objetivo público '{goal}' no tiene mapeo interno definido"
            )
            
            internal_goal = PUBLIC_TO_INTERNAL_GOAL_MAPPING[goal]
            assert internal_goal is not None, f"Mapeo interno para '{goal}' es None"
    
    def test_model_mapping_coverage(self):
        """Verificar que todas las variantes públicas tienen mapeo interno."""
        from src.db.schemas.public_recommendation import PUBLIC_TO_INTERNAL_MODEL_MAPPING
        
        public_variants = ["standard", "premium", "enterprise"]
        
        for variant in public_variants:
            assert variant in PUBLIC_TO_INTERNAL_MODEL_MAPPING, (
                f"Variante pública '{variant}' no tiene mapeo interno definido"
            )
            
            internal_variant = PUBLIC_TO_INTERNAL_MODEL_MAPPING[variant]
            assert internal_variant is not None, f"Mapeo interno para '{variant}' es None"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
