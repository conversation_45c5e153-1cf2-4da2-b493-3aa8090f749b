import React from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import Link from "next/link";
import { generateMetadata as generateSEOMetadata } from '@/lib/seo';
import { ArrowLeft, Rocket, Users, CheckCircle, ArrowRight, Clock, Target } from "lucide-react";

export const metadata = generateSEOMetadata({
  title: 'SaaS B2B - Casos de Uso | Rayuela',
  description: 'Agrega recomendaciones inteligentes a tu SaaS B2B sin equipo de ML. MVP en 1 semana, diferenciación competitiva y mayor engagement.',
  path: '/use-cases/saas-b2b',
  keywords: ['SaaS', 'B2B', 'machine learning', 'MVP', 'recomendaciones', 'diferenciación'],
});

export default function SaasB2BUseCasePage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-white via-sky-50 to-sky-100 dark:from-gray-950 dark:to-gray-900">
      <div className="container mx-auto px-4 py-16">
        {/* Header */}
        <div className="mb-8">
          <Button variant="ghost" asChild className="mb-4">
            <Link href="/#use-cases">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Volver a casos de uso
            </Link>
          </Button>
          
          <div className="flex items-center gap-3 mb-4">
            <Rocket className="w-8 h-8 text-primary" />
            <Badge variant="outline" className="text-sm">SaaS B2B</Badge>
          </div>
          
          <h1 className="text-4xl md:text-5xl font-bold text-foreground mb-4">
            Agrega inteligencia artificial sin equipo de ML
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl mb-8">
            Diferencia tu SaaS B2B con recomendaciones inteligentes. Lanza tu MVP con IA en 1 semana,
            sin contratar data scientists ni montar infraestructura.
          </p>

          {/* Historia de Éxito */}
          <div className="bg-gradient-to-r from-primary/5 to-accent/5 border border-primary/20 rounded-xl p-8 mb-8">
            <div className="flex items-start gap-4">
              <div className="w-16 h-16 bg-gradient-to-r from-primary to-accent rounded-xl flex items-center justify-center flex-shrink-0">
                <span className="text-white text-2xl">🏆</span>
              </div>
              <div>
                <h3 className="text-xl font-semibold text-foreground mb-3">
                  Caso de Éxito: DataFlow agregó IA en 5 días y aumentó engagement 40%
                </h3>
                <p className="text-muted-foreground mb-4 leading-relaxed">
                  <strong>DataFlow</strong>, una startup de analytics con 8 developers y 5,000 usuarios B2B,
                  necesitaba diferenciarse de competidores 10x más grandes. Implementaron Rayuela para recomendaciones inteligentes. En solo 30 días:
                </p>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="bg-card border border-border rounded-lg p-4 text-center">
                    <div className="text-2xl font-bold text-success mb-1">5 días</div>
                    <div className="text-sm text-muted-foreground">MVP con IA</div>
                  </div>
                  <div className="bg-card border border-border rounded-lg p-4 text-center">
                    <div className="text-2xl font-bold text-primary mb-1">+40%</div>
                    <div className="text-sm text-muted-foreground">User engagement</div>
                  </div>
                  <div className="bg-card border border-border rounded-lg p-4 text-center">
                    <div className="text-2xl font-bold text-warning mb-1">$280k</div>
                    <div className="text-sm text-muted-foreground">Ahorrados vs equipo ML</div>
                  </div>
                </div>
                <blockquote className="mt-4 pl-4 border-l-2 border-primary/30 italic text-muted-foreground">
                  "Rayuela nos permitió competir con empresas 10x más grandes. Nuestros usuarios ahora tienen una experiencia
                  inteligente que antes solo veían en productos enterprise. El ROI fue inmediato."
                  <footer className="mt-2 text-sm">
                    — <strong>Alex Chen</strong>, CTO de DataFlow
                  </footer>
                </blockquote>
              </div>
            </div>
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid gap-6 md:grid-cols-3 mb-12">
          <Card className="text-center">
            <CardContent className="pt-6">
              <Clock className="w-12 h-12 text-success mx-auto mb-4" />
              <div className="text-3xl font-bold text-success mb-2">1 semana</div>
              <p className="text-muted-foreground">Time-to-market para MVP con IA</p>
            </CardContent>
          </Card>
          <Card className="text-center">
            <CardContent className="pt-6">
              <Target className="w-12 h-12 text-primary mx-auto mb-4" />
              <div className="text-3xl font-bold text-primary mb-2">+35%</div>
              <p className="text-muted-foreground">Aumento en engagement de usuarios</p>
            </CardContent>
          </Card>
          <Card className="text-center">
            <CardContent className="pt-6">
              <Users className="w-12 h-12 text-warning mx-auto mb-4" />
              <div className="text-3xl font-bold text-warning mb-2">+22%</div>
              <p className="text-muted-foreground">Mejora en retención mensual</p>
            </CardContent>
          </Card>
        </div>

        {/* Problem & Solution */}
        <div className="grid gap-8 md:grid-cols-2 mb-12">
          <Card className="border-destructive/20">
            <CardHeader>
              <CardTitle className="text-destructive">El Desafío del SaaS Moderno</CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-3 text-muted-foreground">
                <li>• <strong>Competencia feroz:</strong> Todos los SaaS se ven iguales</li>
                <li>• <strong>IA es el futuro:</strong> Clientes esperan funcionalidades inteligentes</li>
                <li>• <strong>Talento escaso:</strong> Data scientists cuestan $150k+ anuales</li>
                <li>• <strong>Infraestructura compleja:</strong> ML requiere expertise técnico profundo</li>
                <li>• <strong>Time-to-market lento:</strong> Desarrollar IA interna toma 6-12 meses</li>
              </ul>
            </CardContent>
          </Card>

          <Card className="border-success/20">
            <CardHeader>
              <CardTitle className="text-success">Rayuela: IA como Servicio</CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-3 text-muted-foreground">
                <li>• <strong>Diferenciación inmediata:</strong> IA de nivel enterprise desde día 1</li>
                <li>• <strong>Sin equipo ML:</strong> Tu equipo actual puede implementarlo</li>
                <li>• <strong>Costo predecible:</strong> Desde $99/mes vs $150k+ anuales</li>
                <li>• <strong>Infraestructura incluida:</strong> Escalabilidad automática en la nube</li>
                <li>• <strong>MVP en 1 semana:</strong> Integración simple con API REST</li>
              </ul>
            </CardContent>
          </Card>
        </div>

        {/* SaaS Categories */}
        <Card className="mb-12">
          <CardHeader>
            <CardTitle>Casos por Tipo de SaaS B2B</CardTitle>
            <CardDescription>
              Rayuela se adapta a diferentes verticales de software empresarial
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-8 md:grid-cols-2">
              <div className="space-y-6">
                <div>
                  <h4 className="font-semibold text-lg mb-3">📊 Analytics & BI</h4>
                  <div className="space-y-3">
                    <div className="bg-slate-50 dark:bg-slate-800 p-4 rounded-lg">
                      <h5 className="font-medium mb-2">Dashboards inteligentes</h5>
                      <p className="text-sm text-muted-foreground">
                        "Basándote en tu rol y actividad, estos KPIs son los más relevantes para ti"
                      </p>
                    </div>
                    <div className="bg-slate-50 dark:bg-slate-800 p-4 rounded-lg">
                      <h5 className="font-medium mb-2">Insights automáticos</h5>
                      <p className="text-sm text-muted-foreground">
                        Sugiere qué métricas analizar según patrones de usuarios similares
                      </p>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="font-semibold text-lg mb-3">🛠️ Project Management</h4>
                  <div className="space-y-3">
                    <div className="bg-slate-50 dark:bg-slate-800 p-4 rounded-lg">
                      <h5 className="font-medium mb-2">Asignación inteligente de tareas</h5>
                      <p className="text-sm text-muted-foreground">
                        Recomienda el mejor miembro del equipo para cada tipo de tarea
                      </p>
                    </div>
                    <div className="bg-slate-50 dark:bg-slate-800 p-4 rounded-lg">
                      <h5 className="font-medium mb-2">Templates personalizados</h5>
                      <p className="text-sm text-muted-foreground">
                        Sugiere plantillas de proyecto basándose en industria y tamaño de equipo
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="space-y-6">
                <div>
                  <h4 className="font-semibold text-lg mb-3">💼 CRM & Sales</h4>
                  <div className="space-y-3">
                    <div className="bg-slate-50 dark:bg-slate-800 p-4 rounded-lg">
                      <h5 className="font-medium mb-2">Lead scoring inteligente</h5>
                      <p className="text-sm text-muted-foreground">
                        Prioriza leads basándose en patrones de conversión históricos
                      </p>
                    </div>
                    <div className="bg-slate-50 dark:bg-slate-800 p-4 rounded-lg">
                      <h5 className="font-medium mb-2">Next best action</h5>
                      <p className="text-sm text-muted-foreground">
                        Sugiere la mejor acción para cada lead según su perfil y comportamiento
                      </p>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="font-semibold text-lg mb-3">📚 Learning Management</h4>
                  <div className="space-y-3">
                    <div className="bg-slate-50 dark:bg-slate-800 p-4 rounded-lg">
                      <h5 className="font-medium mb-2">Rutas de aprendizaje personalizadas</h5>
                      <p className="text-sm text-muted-foreground">
                        Recomienda cursos basándose en rol, nivel y objetivos del empleado
                      </p>
                    </div>
                    <div className="bg-slate-50 dark:bg-slate-800 p-4 rounded-lg">
                      <h5 className="font-medium mb-2">Contenido adaptativo</h5>
                      <p className="text-sm text-muted-foreground">
                        Sugiere materiales complementarios según progreso y estilo de aprendizaje
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Implementation Timeline */}
        <Card className="mb-12">
          <CardHeader>
            <CardTitle>Timeline de Implementación</CardTitle>
            <CardDescription>
              De idea a MVP con IA en tiempo récord
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-6 md:grid-cols-4">
              <div className="text-center">
                <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-primary font-bold">Día 1</span>
                </div>
                <h4 className="font-semibold mb-2">Setup inicial</h4>
                <p className="text-sm text-muted-foreground">
                  Registro, configuración de API keys y primeras llamadas de prueba
                </p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-primary font-bold">Día 2-3</span>
                </div>
                <h4 className="font-semibold mb-2">Integración</h4>
                <p className="text-sm text-muted-foreground">
                  Conectar tu base de datos existente y configurar modelos de datos
                </p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-primary font-bold">Día 4-5</span>
                </div>
                <h4 className="font-semibold mb-2">Frontend</h4>
                <p className="text-sm text-muted-foreground">
                  Implementar componentes de recomendaciones en tu interfaz
                </p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 bg-success/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-success font-bold">Día 6-7</span>
                </div>
                <h4 className="font-semibold mb-2">Launch</h4>
                <p className="text-sm text-muted-foreground">
                  Testing, ajustes finales y lanzamiento a usuarios beta
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Competitive Advantage */}
        <Card className="mb-12">
          <CardHeader>
            <CardTitle>Ventaja Competitiva Inmediata</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-6 md:grid-cols-2">
              <div>
                <h4 className="font-semibold text-lg mb-4">🏃‍♂️ Vs. Competidores sin IA</h4>
                <ul className="space-y-2 text-sm text-muted-foreground">
                  <li>• Tu SaaS se siente "inteligente" y moderno</li>
                  <li>• Usuarios completan tareas más eficientemente</li>
                  <li>• Menor curva de aprendizaje para nuevos usuarios</li>
                  <li>• Diferenciación clara en demos y sales calls</li>
                  <li>• Justificación para pricing premium</li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold text-lg mb-4">🐌 Vs. Competidores desarrollando IA</h4>
                <ul className="space-y-2 text-sm text-muted-foreground">
                  <li>• 6-12 meses de ventaja en time-to-market</li>
                  <li>• Ellos gastan $500k+, tú $1,200/año</li>
                  <li>• Tu IA mejora automáticamente, la de ellos requiere mantenimiento</li>
                  <li>• Puedes iterar y experimentar más rápido</li>
                  <li>• Menor riesgo técnico y financiero</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Technical Benefits */}
        <Card className="mb-12">
          <CardHeader>
            <CardTitle>Beneficios Técnicos para Startups</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2">
              <div className="flex items-start gap-3">
                <CheckCircle className="w-5 h-5 text-success mt-1" />
                <div>
                  <h4 className="font-semibold">Zero ML expertise required</h4>
                  <p className="text-sm text-muted-foreground">
                    Tu equipo de desarrollo actual puede implementarlo sin aprender ML
                  </p>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <CheckCircle className="w-5 h-5 text-success mt-1" />
                <div>
                  <h4 className="font-semibold">Escalabilidad automática</h4>
                  <p className="text-sm text-muted-foreground">
                    De 10 a 10M usuarios sin cambios en tu código
                  </p>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <CheckCircle className="w-5 h-5 text-success mt-1" />
                <div>
                  <h4 className="font-semibold">Costo predecible</h4>
                  <p className="text-sm text-muted-foreground">
                    Pricing basado en uso, no en infraestructura fija
                  </p>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <CheckCircle className="w-5 h-5 text-success mt-1" />
                <div>
                  <h4 className="font-semibold">Compliance incluido</h4>
                  <p className="text-sm text-muted-foreground">
                    SOC 2, GDPR y otras certificaciones ya cubiertas
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* ROI for Startups */}
        <Card className="mb-12">
          <CardHeader>
            <CardTitle>ROI para Startups</CardTitle>
            <CardDescription>
              Comparación: Rayuela vs. Equipo ML interno
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-6 md:grid-cols-2">
              <div className="space-y-4">
                <h4 className="font-semibold text-destructive">❌ Equipo ML Interno</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Data Scientist Senior:</span>
                    <span className="font-mono">$150,000/año</span>
                  </div>
                  <div className="flex justify-between">
                    <span>ML Engineer:</span>
                    <span className="font-mono">$130,000/año</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Infraestructura (AWS/GCP):</span>
                    <span className="font-mono">$24,000/año</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Herramientas y licencias:</span>
                    <span className="font-mono">$12,000/año</span>
                  </div>
                  <div className="flex justify-between border-t pt-2">
                    <span className="font-semibold">Total primer año:</span>
                    <span className="font-mono font-bold text-destructive">$316,000</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="font-semibold">Time-to-market:</span>
                    <span className="font-mono font-bold text-destructive">6-12 meses</span>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <h4 className="font-semibold text-success">✅ Con Rayuela</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Rayuela Professional:</span>
                    <span className="font-mono">$1,200/año</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Desarrollo (1 semana):</span>
                    <span className="font-mono">$2,000</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Infraestructura adicional:</span>
                    <span className="font-mono">$0</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Mantenimiento ML:</span>
                    <span className="font-mono">$0</span>
                  </div>
                  <div className="flex justify-between border-t pt-2">
                    <span className="font-semibold">Total primer año:</span>
                    <span className="font-mono font-bold text-success">$3,200</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="font-semibold">Time-to-market:</span>
                    <span className="font-mono font-bold text-success">1 semana</span>
                  </div>
                  <div className="flex justify-between border-t pt-2">
                    <span className="font-semibold text-success">Ahorro:</span>
                    <span className="font-mono font-bold text-success">$312,800</span>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* CTA Section */}
        <Card className="bg-gradient-to-r from-primary/10 to-primary/5 border-primary/20">
          <CardContent className="pt-8">
            <div className="text-center">
              <h3 className="text-2xl font-bold mb-4">
                ¿Listo para diferenciarte con IA?
              </h3>
              <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
                Únete a más de 100 startups B2B que ya usan Rayuela para agregar inteligencia 
                artificial a sus productos sin equipo de ML ni infraestructura compleja.
              </p>
              <div className="flex gap-4 justify-center flex-wrap">
                <Button asChild size="lg">
                  <Link href="/register">
                    Empezar gratis
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Link>
                </Button>
                <Button variant="outline" asChild size="lg">
                  <Link href="/contact-sales">
                    Demo técnica
                  </Link>
                </Button>
              </div>
              <p className="text-xs text-muted-foreground mt-4">
                Sin tarjeta de crédito • MVP en 1 semana • Soporte técnico incluido
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
