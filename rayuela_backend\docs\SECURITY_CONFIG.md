# 🔐 Configuración de Seguridad - Protección IP

## Variables de Entorno de Seguridad

### Configuración de Logging
```bash
# Nivel de logging para eventos de seguridad
SECURITY_LOG_LEVEL=WARNING

# Habilitar logs de acceso a OpenAPI
OPENAPI_ACCESS_LOGGING=true

# Formato de logs de seguridad
SECURITY_LOG_FORMAT=json
```

### Configuración de Monitoreo
```bash
# Habilitar alertas de seguridad
SECURITY_ALERTS_ENABLED=true

# Webhook para alertas críticas
SECURITY_WEBHOOK_URL=https://hooks.slack.com/services/...

# Umbral de accesos sospechosos por IP
SUSPICIOUS_ACCESS_THRESHOLD=10
```

## Configuración de Endpoints

### Endpoints Públicos (Sin Autenticación)
```python
PUBLIC_ENDPOINTS = [
    "/api/v1/auth",
    "/api/v1/health", 
    "/api/v1/accounts",
    "/api/v1/plans",
    "/api/v1/public-openapi.json"  # Endpoint protegido por IP
]
```

### Endpoints Protegidos (Requieren Autenticación)
```python
PROTECTED_ENDPOINTS = [
    "/api/v1/openapi.json",        # OpenAPI completo
    "/api/v1/recommendations",     # API principal
    "/api/v1/analytics",           # Métricas internas
    "/api/v1/admin"                # Administración
]
```

## Términos Sensibles Protegidos

### Algoritmos de Machine Learning
```python
ML_ALGORITHM_TERMS = [
    "collaborative",
    "content-based", 
    "hybrid",
    "matrix_factorization",
    "deep_learning",
    "neural_network"
]
```

### Métricas de Evaluación
```python
ML_METRICS_TERMS = [
    "precision",
    "recall", 
    "ndcg",
    "map_score",
    "auc_roc",
    "f1_score",
    "catalog_coverage",
    "diversity",
    "novelty",
    "serendipity"
]
```

### Parámetros Técnicos
```python
TECHNICAL_PARAMS = [
    "model_type",
    "strategy",
    "algorithm_type",
    "learning_rate",
    "regularization",
    "embedding_dim",
    "num_factors",
    "iterations"
]
```

## Mapeos de Conversión

### Goals (Objetivos de Recomendación)
```python
GOAL_MAPPING = {
    # Interno → Público
    "maximize_engagement": "engagement",
    "discover_diverse": "discovery", 
    "promote_new_arrivals": "freshness",
    "balanced": "balanced",
    "optimize_conversion": "performance",
    "increase_retention": "loyalty"
}

# Mapeo inverso para conversión de requests
PUBLIC_TO_INTERNAL_GOAL_MAPPING = {
    "engagement": "maximize_engagement",
    "discovery": "discover_diverse",
    "freshness": "promote_new_arrivals", 
    "balanced": "balanced",
    "performance": "optimize_conversion",
    "loyalty": "increase_retention"
}
```

### Model Variants (Variantes de Modelo)
```python
MODEL_MAPPING = {
    # Interno → Público
    "collaborative": "standard",
    "content_based": "advanced",
    "hybrid": "premium",
    "deep_learning": "ai_powered",
    "ensemble": "enterprise"
}

# Mapeo inverso
PUBLIC_TO_INTERNAL_MODEL_MAPPING = {
    "standard": "collaborative",
    "advanced": "content_based", 
    "premium": "hybrid",
    "ai_powered": "deep_learning",
    "enterprise": "ensemble"
}
```

## Configuración de Monitoreo

### Eventos de Seguridad
```python
SECURITY_EVENTS = {
    "public_openapi_access": {
        "level": "INFO",
        "critical": False,
        "alert_threshold": 100  # requests per hour
    },
    "private_openapi_access": {
        "level": "WARNING", 
        "critical": True,
        "alert_threshold": 10   # requests per hour
    },
    "suspicious_pattern": {
        "level": "ERROR",
        "critical": True,
        "alert_threshold": 1
    }
}
```

### Configuración de Alertas
```python
ALERT_CONFIG = {
    "slack": {
        "enabled": True,
        "webhook_url": os.getenv("SECURITY_WEBHOOK_URL"),
        "channel": "#security-alerts",
        "critical_only": True
    },
    "email": {
        "enabled": False,
        "recipients": ["<EMAIL>"],
        "smtp_server": "smtp.rayuela.com"
    },
    "syslog": {
        "enabled": True,
        "facility": "LOG_AUTH",
        "priority": "LOG_WARNING"
    }
}
```

## Configuración de Rate Limiting

### Límites por Endpoint
```python
RATE_LIMITS = {
    "/api/v1/public-openapi.json": {
        "requests_per_minute": 60,
        "requests_per_hour": 1000,
        "burst_limit": 10
    },
    "/api/v1/openapi.json": {
        "requests_per_minute": 10,
        "requests_per_hour": 100,
        "burst_limit": 3
    }
}
```

### Configuración por IP
```python
IP_RATE_LIMITS = {
    "default": {
        "requests_per_minute": 100,
        "requests_per_hour": 2000
    },
    "whitelist": [
        "***********/24",    # Red interna
        "10.0.0.0/8",        # VPN corporativa
    ],
    "blacklist": [
        "suspicious_ips.txt"  # Lista dinámica
    ]
}
```

## Headers de Seguridad

### Headers Requeridos
```python
SECURITY_HEADERS = {
    "X-Content-Type-Options": "nosniff",
    "X-Frame-Options": "DENY", 
    "X-XSS-Protection": "1; mode=block",
    "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
    "Content-Security-Policy": "default-src 'self'",
    "Referrer-Policy": "strict-origin-when-cross-origin"
}
```

### Headers de API
```python
API_HEADERS = {
    "X-API-Version": "v1",
    "X-Rate-Limit-Remaining": "dynamic",
    "X-Security-Level": "protected",  # Para endpoints privados
    "X-IP-Protection": "active"       # Para endpoints públicos
}
```

## Configuración de Auditoría

### Eventos Auditados
```python
AUDIT_EVENTS = [
    "openapi_access",
    "authentication_failure", 
    "rate_limit_exceeded",
    "suspicious_request_pattern",
    "ip_blacklist_hit",
    "security_header_missing"
]
```

### Retención de Logs
```python
LOG_RETENTION = {
    "security_logs": "1 year",
    "access_logs": "6 months", 
    "audit_logs": "2 years",
    "error_logs": "3 months"
}
```

## Configuración de Desarrollo vs Producción

### Desarrollo
```python
DEVELOPMENT_CONFIG = {
    "security_logging": "DEBUG",
    "rate_limiting": False,
    "ip_protection": "permissive",
    "alert_webhooks": False
}
```

### Producción
```python
PRODUCTION_CONFIG = {
    "security_logging": "WARNING",
    "rate_limiting": True,
    "ip_protection": "strict", 
    "alert_webhooks": True,
    "fail_secure": True
}
```

## Validación de Configuración

### Checklist de Seguridad
```python
def validate_security_config():
    checks = [
        "PUBLIC_ENDPOINTS defined",
        "SENSITIVE_TERMS configured",
        "GOAL_MAPPING complete",
        "MODEL_MAPPING complete", 
        "SECURITY_HEADERS present",
        "RATE_LIMITS configured",
        "AUDIT_EVENTS enabled",
        "ALERT_CONFIG valid"
    ]
    return all(check_passed(check) for check in checks)
```

### Tests de Configuración
```bash
# Validar configuración de seguridad
python -m pytest tests/test_security_config.py

# Verificar mapeos completos
python -m pytest tests/test_ip_protection.py::TestConversionMapping

# Validar monitoreo
python -m pytest tests/test_ip_protection.py::TestSecurityMonitoring
```

---

**Nota**: Esta configuración debe ser revisada y actualizada regularmente por el equipo de seguridad. Cualquier cambio debe ser probado en entorno de desarrollo antes de aplicarse en producción.
