# backend/src/api/v1/api.py
from fastapi import APIRouter
from src.api.v1.endpoints import (
    users,
    products,
    recommendations,
    system_users,
    accounts,
    auth,
    interactions,
    pipeline,
    test_cache,
    health,
    analytics,
    billing,
    roles,
    permissions,
    maintenance,
    subscription_usage,
    storage_usage,
    data_ingestion,
    usage_summary,
    usage_history,
    plans,
    api_keys,
    sandbox,
    public_openapi,
)

# Router para endpoints públicos (sin autenticación)
public_router = APIRouter()
public_router.include_router(health.router, tags=["health"])  # Health check endpoints
public_router.include_router(auth.router, prefix="/auth", tags=["auth"])
public_router.include_router(accounts.router, prefix="/accounts", tags=["accounts"])
public_router.include_router(plans.router, prefix="/plans", tags=["plans"])
# SECURITY: Public OpenAPI endpoint for IP protection
public_router.include_router(public_openapi.router, tags=["openapi"])
# SECURITY: Admin endpoints removed from public routes
# Admin functionality should be handled through secure scripts or protected endpoints

# Router para endpoints protegidos
private_router = APIRouter()
private_router.include_router(
    system_users.router, prefix="/system-users", tags=["system-users"]
)
private_router.include_router(users.router, prefix="/end-users", tags=["end-users"])
private_router.include_router(products.router, prefix="/products", tags=["products"])
private_router.include_router(
    recommendations.router, prefix="/recommendations", tags=["recommendations"]
)
private_router.include_router(
    interactions.router, prefix="/interactions", tags=["interactions"]
)
private_router.include_router(pipeline.router, prefix="/pipeline", tags=["pipeline"])
private_router.include_router(test_cache.router, prefix="/cache", tags=["cache"])
private_router.include_router(analytics.router, prefix="/analytics", tags=["analytics"])
private_router.include_router(billing.router, prefix="/billing", tags=["billing"])
private_router.include_router(roles.router, prefix="/roles", tags=["roles"])
private_router.include_router(permissions.router, prefix="/permissions", tags=["permissions"])
private_router.include_router(maintenance.router, prefix="/maintenance", tags=["maintenance"])
private_router.include_router(subscription_usage.router, prefix="/subscription", tags=["subscription"])
private_router.include_router(storage_usage.router, prefix="/storage", tags=["storage"])
private_router.include_router(data_ingestion.router, prefix="/ingestion", tags=["ingestion"])
private_router.include_router(usage_summary.router, prefix="/usage", tags=["usage"])
private_router.include_router(usage_history.router, prefix="/usage", tags=["usage"])
private_router.include_router(api_keys.router, prefix="/api-keys", tags=["api-keys"])
private_router.include_router(sandbox.router, prefix="/sandbox", tags=["sandbox"])
