"""
Tests for the unified interaction creation endpoint with external ID support.
"""
import pytest
from fastapi.testclient import TestClient
from sqlalchemy.ext.asyncio import AsyncSession
from src.db.models.account import Account
from src.db.models.end_user import EndUser
from src.db.models.product import Product
from src.db.models.interaction import Interaction
from src.db.enums import InteractionType
from decimal import Decimal


@pytest.mark.asyncio
class TestInteractionExternalIds:
    """Test class for interaction creation with external IDs."""

    async def test_create_interaction_with_external_ids(
        self, 
        async_client: TestClient, 
        db_session: AsyncSession,
        test_account: Account,
        test_api_key: str
    ):
        """Test creating an interaction using external IDs."""
        # Create test user and product
        user = EndUser(
            account_id=test_account.account_id,
            external_id="test_user_123",
            is_active=True
        )
        db_session.add(user)
        await db_session.flush()

        product = Product(
            account_id=test_account.account_id,
            external_id="test_product_456",
            name="Test Product",
            price=Decimal("99.99"),
            category="test",
            is_active=True
        )
        db_session.add(product)
        await db_session.commit()

        # Create interaction using external IDs
        interaction_data = {
            "external_user_id": "test_user_123",
            "external_product_id": "test_product_456",
            "interaction_type": "VIEW",
            "value": 1.0,
            "recommendation_metadata": {
                "source": "test",
                "algorithm": "test_algo"
            }
        }

        response = await async_client.post(
            "/api/v1/interactions/",
            json=interaction_data,
            headers={"X-API-Key": test_api_key}
        )

        assert response.status_code == 200
        data = response.json()
        assert data["user_id"] == user.user_id
        assert data["product_id"] == product.product_id
        assert data["interaction_type"] == "VIEW"
        assert float(data["value"]) == 1.0
        assert data["recommendation_metadata"]["source"] == "test"

    async def test_create_interaction_with_internal_ids(
        self, 
        async_client: TestClient, 
        db_session: AsyncSession,
        test_account: Account,
        test_api_key: str
    ):
        """Test creating an interaction using internal IDs (legacy support)."""
        # Create test user and product
        user = EndUser(
            account_id=test_account.account_id,
            external_id="test_user_internal",
            is_active=True
        )
        db_session.add(user)
        await db_session.flush()

        product = Product(
            account_id=test_account.account_id,
            external_id="test_product_internal",
            name="Test Product Internal",
            price=Decimal("49.99"),
            category="test",
            is_active=True
        )
        db_session.add(product)
        await db_session.commit()

        # Create interaction using internal IDs
        interaction_data = {
            "user_id": user.user_id,
            "product_id": product.product_id,
            "interaction_type": "PURCHASE",
            "value": 2.0
        }

        response = await async_client.post(
            "/api/v1/interactions/",
            json=interaction_data,
            headers={"X-API-Key": test_api_key}
        )

        assert response.status_code == 200
        data = response.json()
        assert data["user_id"] == user.user_id
        assert data["product_id"] == product.product_id
        assert data["interaction_type"] == "PURCHASE"
        assert float(data["value"]) == 2.0

    async def test_create_interaction_validation_errors(
        self, 
        async_client: TestClient, 
        test_api_key: str
    ):
        """Test validation errors for interaction creation."""
        
        # Test missing both ID types
        response = await async_client.post(
            "/api/v1/interactions/",
            json={
                "interaction_type": "VIEW",
                "value": 1.0
            },
            headers={"X-API-Key": test_api_key}
        )
        assert response.status_code == 422
        
        # Test mixing ID types
        response = await async_client.post(
            "/api/v1/interactions/",
            json={
                "user_id": 1,
                "external_product_id": "test_product",
                "interaction_type": "VIEW",
                "value": 1.0
            },
            headers={"X-API-Key": test_api_key}
        )
        assert response.status_code == 422
        
        # Test partial external IDs
        response = await async_client.post(
            "/api/v1/interactions/",
            json={
                "external_user_id": "test_user",
                "interaction_type": "VIEW",
                "value": 1.0
            },
            headers={"X-API-Key": test_api_key}
        )
        assert response.status_code == 422

    async def test_create_interaction_nonexistent_external_ids(
        self, 
        async_client: TestClient, 
        test_api_key: str
    ):
        """Test error handling for non-existent external IDs."""
        
        interaction_data = {
            "external_user_id": "nonexistent_user",
            "external_product_id": "nonexistent_product",
            "interaction_type": "VIEW",
            "value": 1.0
        }

        response = await async_client.post(
            "/api/v1/interactions/",
            json=interaction_data,
            headers={"X-API-Key": test_api_key}
        )

        assert response.status_code == 404

    async def test_create_interaction_rating_validation(
        self, 
        async_client: TestClient, 
        db_session: AsyncSession,
        test_account: Account,
        test_api_key: str
    ):
        """Test rating value validation for RATING interactions."""
        # Create test user and product
        user = EndUser(
            account_id=test_account.account_id,
            external_id="test_user_rating",
            is_active=True
        )
        db_session.add(user)
        await db_session.flush()

        product = Product(
            account_id=test_account.account_id,
            external_id="test_product_rating",
            name="Test Product Rating",
            price=Decimal("29.99"),
            category="test",
            is_active=True
        )
        db_session.add(product)
        await db_session.commit()

        # Test valid rating
        interaction_data = {
            "external_user_id": "test_user_rating",
            "external_product_id": "test_product_rating",
            "interaction_type": "RATING",
            "value": 4.5
        }

        response = await async_client.post(
            "/api/v1/interactions/",
            json=interaction_data,
            headers={"X-API-Key": test_api_key}
        )

        assert response.status_code == 200
        data = response.json()
        assert data["interaction_type"] == "RATING"
        assert float(data["value"]) == 4.5

    async def test_interaction_service_resolves_external_ids_correctly(
        self, 
        db_session: AsyncSession,
        test_account: Account
    ):
        """Test that the InteractionService correctly resolves external IDs to internal IDs."""
        from src.services.interaction_service import InteractionService
        from src.db import schemas
        from unittest.mock import AsyncMock

        # Create test user and product
        user = EndUser(
            account_id=test_account.account_id,
            external_id="service_test_user",
            is_active=True
        )
        db_session.add(user)
        await db_session.flush()

        product = Product(
            account_id=test_account.account_id,
            external_id="service_test_product",
            name="Service Test Product",
            price=Decimal("19.99"),
            category="test",
            is_active=True
        )
        db_session.add(product)
        await db_session.commit()

        # Mock dependencies
        mock_redis = AsyncMock()
        mock_limit_service = AsyncMock()
        mock_limit_service.validate_interaction_limit = AsyncMock()

        # Create service
        service = InteractionService(
            db=db_session,
            account_id=test_account.account_id,
            redis=mock_redis,
            limit_service=mock_limit_service
        )

        # Create interaction data with external IDs
        interaction_data = schemas.InteractionCreate(
            external_user_id="service_test_user",
            external_product_id="service_test_product",
            interaction_type=InteractionType.VIEW,
            value=Decimal("1.0")
        )

        # Create interaction
        result = await service.create_interaction(interaction_data)

        # Verify the interaction was created with correct internal IDs
        assert result.user_id == user.user_id
        assert result.product_id == product.product_id
        assert result.interaction_type == InteractionType.VIEW
        assert result.value == Decimal("1.0")
        assert result.account_id == test_account.account_id
