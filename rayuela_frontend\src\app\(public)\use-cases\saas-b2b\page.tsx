import React from "react";
import Link from "next/link";
import { generateMetadata as generateSEOMetadata } from '@/lib/seo';
import { ArrowLeft, Rocket, Users, Clock, Target, Zap } from "lucide-react";

export const metadata = generateSEOMetadata({
  title: 'SaaS B2B - Casos de Uso | Rayuela',
  description: 'Agrega recomendaciones inteligentes a tu SaaS B2B sin equipo de ML. MVP en 1 semana, diferenciación competitiva y mayor engagement.',
  path: '/use-cases/saas-b2b',
  keywords: ['SaaS', 'B2B', 'machine learning', 'MVP', 'recomendaciones', 'diferenciación'],
});

export default function SaasB2BUseCasePage() {
  return (
    <main className="bg-white">
      {/* Breadcrumb */}
      <section className="py-6 border-b border-gray-100">
        <div className="max-w-7xl mx-auto px-6">
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <Link href="/" className="hover:text-purple-600 transition-colors flex items-center gap-1">
              <ArrowLeft className="w-4 h-4" />
              Inicio
            </Link>
            <span>/</span>
            <span className="text-gray-900 font-medium">SaaS B2B</span>
          </div>
        </div>
      </section>

      {/* Hero Section */}
      <section className="py-14 md:py-16">
        <div className="max-w-7xl mx-auto px-6">
          <div className="text-center mb-12">
            <div className="inline-flex items-center gap-2 bg-purple-50 text-purple-700 px-4 py-2 rounded-full text-sm font-medium mb-6">
              <span>📊</span>{" "}
              Caso de Uso: SaaS B2B
            </div>
            <h1 className="text-4xl md:text-5xl font-bold leading-tight mb-6">
              Agrega inteligencia artificial sin equipo de ML
            </h1>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto mb-8">
              Diferencia tu SaaS B2B con recomendaciones inteligentes. Lanza tu MVP con IA en 1 semana,
              sin contratar data scientists ni montar infraestructura.
            </p>
          </div>

          {/* Historia de Éxito */}
          <div className="card max-w-5xl mx-auto">
            <div className="flex items-start gap-4">
              <div className="w-16 h-16 bg-gradient-to-r from-purple-600 to-purple-700 rounded-xl flex items-center justify-center flex-shrink-0">
                <span className="text-white text-2xl">🏆</span>
              </div>
              <div className="flex-1">
                <h3 className="text-xl font-bold text-gray-900 mb-3">
                  Caso de Éxito: DataFlow agregó IA en 5 días y aumentó engagement 40%
                </h3>
                <p className="text-gray-600 mb-6 leading-relaxed">
                  <strong>DataFlow</strong>, una startup de analytics con 8 developers y 5,000 usuarios B2B, 
                  necesitaba diferenciarse con IA pero no tenía equipo de ML. Con Rayuela implementaron recomendaciones en 5 días:
                </p>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                  <div className="bg-green-50 border border-green-200 rounded-lg p-4 text-center">
                    <div className="text-2xl font-bold text-green-600 mb-1">+40%</div>
                    <div className="text-sm text-gray-600">User engagement</div>
                  </div>
                  <div className="bg-purple-50 border border-purple-200 rounded-lg p-4 text-center">
                    <div className="text-2xl font-bold text-purple-600 mb-1">$280k</div>
                    <div className="text-sm text-gray-600">Ahorrados vs equipo ML</div>
                  </div>
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 text-center">
                    <div className="text-2xl font-bold text-blue-600 mb-1">5 días</div>
                    <div className="text-sm text-gray-600">Time to market</div>
                  </div>
                </div>
                <blockquote className="pl-4 border-l-4 border-purple-200 italic text-gray-600 bg-gray-50 p-4 rounded-r-lg">
                  "Rayuela nos permitió competir con gigantes que tienen equipos de ML de 50+ personas. 
                  Ahora nuestros usuarios descubren insights que no sabían que necesitaban."
                  <footer className="mt-2 text-sm not-italic">
                    — <strong className="text-gray-900">Alex Chen</strong>, CTO de DataFlow
                  </footer>
                </blockquote>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Key Metrics */}
      <section className="section-gradient-strong">
        <div className="max-w-7xl mx-auto px-6">
          <div className="grid gap-6 md:grid-cols-3">
            <div className="card text-center">
              <Clock className="w-12 h-12 text-green-600 mx-auto mb-4" />
              <div className="text-3xl font-bold text-green-600 mb-2">1 semana</div>
              <p className="text-gray-600">De idea a MVP con IA funcionando</p>
            </div>
            <div className="card text-center">
              <Target className="w-12 h-12 text-purple-600 mx-auto mb-4" />
              <div className="text-3xl font-bold text-purple-600 mb-2">$280k</div>
              <p className="text-gray-600">Ahorrados vs contratar equipo ML</p>
            </div>
            <div className="card text-center">
              <Users className="w-12 h-12 text-blue-600 mx-auto mb-4" />
              <div className="text-3xl font-bold text-blue-600 mb-2">+35%</div>
              <p className="text-gray-600">Incremento en engagement promedio</p>
            </div>
          </div>
        </div>
      </section>

      {/* Problem & Solution */}
      <section className="py-14 md:py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-6">
          <div className="grid gap-8 md:grid-cols-2">
            <div className="card border-red-200 bg-red-50">
              <h3 className="text-xl font-bold text-red-700 mb-4">El Problema</h3>
              <ul className="space-y-3 text-gray-600">
                <li>• <strong>Competencia con IA:</strong> Grandes empresas tienen equipos de ML</li>
                <li>• <strong>Costos prohibitivos:</strong> Data scientists cuestan $150k+/año</li>
                <li>• <strong>Time to market lento:</strong> 6-12 meses para MVP con ML</li>
                <li>• <strong>Complejidad técnica:</strong> Infraestructura, modelos, mantenimiento</li>
                <li>• <strong>Riesgo de fracaso:</strong> 70% de proyectos ML no llegan a producción</li>
              </ul>
            </div>

            <div className="card border-green-200 bg-green-50">
              <h3 className="text-xl font-bold text-green-700 mb-4">La Solución Rayuela</h3>
              <ul className="space-y-3 text-gray-600">
                <li>• <strong>IA plug-and-play:</strong> API lista para usar, sin setup complejo</li>
                <li>• <strong>Costo predecible:</strong> $99-$499/mes vs $280k/año de equipo</li>
                <li>• <strong>MVP en días:</strong> Integración en 1 semana, no meses</li>
                <li>• <strong>Zero infraestructura:</strong> Todo en la nube, escalabilidad automática</li>
                <li>• <strong>Éxito garantizado:</strong> Funciona desde el día 1, mejora continua</li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Casos de Uso SaaS */}
      <section className="py-14 md:py-16">
        <div className="max-w-7xl mx-auto px-6">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Casos de uso específicos para SaaS B2B</h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Diferentes formas de agregar IA a tu producto según tu industria
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="card hover:shadow-lg transition-all duration-300">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                <span className="text-blue-600 text-xl">📊</span>
              </div>
              <h4 className="font-bold text-lg mb-3">Analytics & BI</h4>
              <p className="text-gray-600 mb-4 text-sm">
                Recomienda dashboards, métricas y insights relevantes según el rol y comportamiento del usuario.
              </p>
              <div className="text-xs text-purple-600 font-semibold">
                Ejemplo: "Usuarios como tú también analizan estas métricas"
              </div>
            </div>

            <div className="card hover:shadow-lg transition-all duration-300">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4">
                <span className="text-green-600 text-xl">🎯</span>
              </div>
              <h4 className="font-bold text-lg mb-3">Marketing Tools</h4>
              <p className="text-gray-600 mb-4 text-sm">
                Sugiere audiencias, canales y estrategias basadas en campañas exitosas similares.
              </p>
              <div className="text-xs text-purple-600 font-semibold">
                Ejemplo: "Empresas similares usan estos canales con éxito"
              </div>
            </div>

            <div className="card hover:shadow-lg transition-all duration-300">
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4">
                <span className="text-purple-600 text-xl">🛠️</span>
              </div>
              <h4 className="font-bold text-lg mb-3">Dev Tools</h4>
              <p className="text-gray-600 mb-4 text-sm">
                Recomienda librerías, patrones de código y mejores prácticas según el contexto del proyecto.
              </p>
              <div className="text-xs text-purple-600 font-semibold">
                Ejemplo: "Proyectos similares usan estas dependencias"
              </div>
            </div>

            <div className="card hover:shadow-lg transition-all duration-300">
              <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mb-4">
                <span className="text-orange-600 text-xl">💼</span>
              </div>
              <h4 className="font-bold text-lg mb-3">CRM & Sales</h4>
              <p className="text-gray-600 mb-4 text-sm">
                Sugiere leads, estrategias de outreach y momentos óptimos para contactar.
              </p>
              <div className="text-xs text-purple-600 font-semibold">
                Ejemplo: "Contacta estos leads ahora (alta probabilidad)"
              </div>
            </div>

            <div className="card hover:shadow-lg transition-all duration-300">
              <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mb-4">
                <span className="text-red-600 text-xl">📚</span>
              </div>
              <h4 className="font-bold text-lg mb-3">Learning Platforms</h4>
              <p className="text-gray-600 mb-4 text-sm">
                Personaliza rutas de aprendizaje y recomienda contenido según objetivos y progreso.
              </p>
              <div className="text-xs text-purple-600 font-semibold">
                Ejemplo: "Siguiente curso recomendado para tu rol"
              </div>
            </div>

            <div className="card hover:shadow-lg transition-all duration-300">
              <div className="w-12 h-12 bg-teal-100 rounded-lg flex items-center justify-center mb-4">
                <span className="text-teal-600 text-xl">🏢</span>
              </div>
              <h4 className="font-bold text-lg mb-3">HR & Recruiting</h4>
              <p className="text-gray-600 mb-4 text-sm">
                Conecta candidatos con posiciones y recomienda perfiles según fit cultural y técnico.
              </p>
              <div className="text-xs text-purple-600 font-semibold">
                Ejemplo: "Candidatos ideales para esta posición"
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* ROI Calculator */}
      <section className="py-14 md:py-16 section-gradient-strong">
        <div className="max-w-7xl mx-auto px-6">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">ROI: Rayuela vs Equipo de ML interno</h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Comparación real de costos y tiempo para agregar IA a tu SaaS
            </p>
          </div>
          
          <div className="card max-w-6xl mx-auto">
            <div className="grid gap-8 md:grid-cols-2">
              <div className="space-y-4">
                <h4 className="font-bold text-lg text-red-700">Opción 1: Equipo ML Interno</h4>
                <div className="space-y-3 text-sm">
                  <div className="flex justify-between p-3 bg-red-50 rounded border border-red-200">
                    <span>Senior ML Engineer:</span>
                    <span className="font-mono font-semibold">$150k/año</span>
                  </div>
                  <div className="flex justify-between p-3 bg-red-50 rounded border border-red-200">
                    <span>Data Scientist:</span>
                    <span className="font-mono font-semibold">$130k/año</span>
                  </div>
                  <div className="flex justify-between p-3 bg-red-50 rounded border border-red-200">
                    <span>Infraestructura ML:</span>
                    <span className="font-mono font-semibold">$24k/año</span>
                  </div>
                  <div className="flex justify-between p-3 bg-red-50 rounded border border-red-200">
                    <span>Tiempo hasta MVP:</span>
                    <span className="font-mono font-semibold">6-12 meses</span>
                  </div>
                  <div className="flex justify-between p-3 bg-red-100 rounded border-l-4 border-red-500">
                    <span className="font-semibold">Costo primer año:</span>
                    <span className="font-mono font-bold text-red-700">$304k</span>
                  </div>
                  <div className="flex justify-between p-3 bg-red-200 rounded border-l-4 border-red-600">
                    <span className="font-bold">Riesgo de fracaso:</span>
                    <span className="font-mono font-bold text-red-800">70%</span>
                  </div>
                </div>
              </div>
              
              <div className="space-y-4">
                <h4 className="font-bold text-lg text-green-700">Opción 2: Rayuela API</h4>
                <div className="space-y-3 text-sm">
                  <div className="flex justify-between p-3 bg-green-50 rounded border border-green-200">
                    <span>Plan Professional:</span>
                    <span className="font-mono font-semibold text-green-700">$499/mes</span>
                  </div>
                  <div className="flex justify-between p-3 bg-green-50 rounded border border-green-200">
                    <span>Integración (1 dev, 1 semana):</span>
                    <span className="font-mono font-semibold text-green-700">$2k</span>
                  </div>
                  <div className="flex justify-between p-3 bg-green-50 rounded border border-green-200">
                    <span>Mantenimiento:</span>
                    <span className="font-mono font-semibold text-green-700">$0</span>
                  </div>
                  <div className="flex justify-between p-3 bg-green-50 rounded border border-green-200">
                    <span>Tiempo hasta MVP:</span>
                    <span className="font-mono font-semibold text-green-700">1 semana</span>
                  </div>
                  <div className="flex justify-between p-3 bg-green-100 rounded border-l-4 border-green-500">
                    <span className="font-semibold">Costo primer año:</span>
                    <span className="font-mono font-bold text-green-700">$8k</span>
                  </div>
                  <div className="flex justify-between p-3 bg-green-200 rounded border-l-4 border-green-600">
                    <span className="font-bold">Riesgo de fracaso:</span>
                    <span className="font-mono font-bold text-green-800">0%</span>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="mt-8 p-6 bg-purple-50 rounded-lg border border-purple-200">
              <div className="text-center">
                <div className="text-purple-700 font-semibold mb-2">💡 Ahorro con Rayuela: $296k en el primer año</div>
                <div className="text-purple-600 text-sm">
                  <strong>3,700% más barato</strong> • <strong>26x más rápido</strong> • <strong>Sin riesgo técnico</strong>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonios */}
      <section className="py-14 md:py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-6">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Lo que dicen nuestros clientes SaaS</h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Startups y scale-ups que ya están compitiendo con IA
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
            <div className="card hover:shadow-lg transition-all duration-300">
              <blockquote className="text-gray-600 mb-4 leading-relaxed italic">
                "Rayuela nos permitió lanzar features de IA que nuestros competidores tardarían años en desarrollar. Game changer total."
              </blockquote>
              <div className="flex items-center gap-3 mb-3">
                <div className="w-12 h-12 bg-gradient-to-r from-purple-400 to-purple-600 rounded-full flex items-center justify-center">
                  <span className="text-white text-xl">👨‍💻</span>
                </div>
                <div>
                  <div className="font-bold text-gray-900">Alex Chen</div>
                  <div className="text-sm text-gray-600">CTO</div>
                  <div className="text-xs text-gray-500">DataFlow</div>
                </div>
              </div>
              <div className="bg-green-50 border border-green-200 rounded-lg px-3 py-2">
                <span className="text-xs font-semibold text-green-700">+40% engagement, $280k ahorrados</span>
              </div>
            </div>

            <div className="card hover:shadow-lg transition-all duration-300">
              <blockquote className="text-gray-600 mb-4 leading-relaxed italic">
                "Nuestros usuarios ahora reciben recomendaciones que realmente necesitan. El engagement se disparó desde el primer día."
              </blockquote>
              <div className="flex items-center gap-3 mb-3">
                <div className="w-12 h-12 bg-gradient-to-r from-blue-400 to-blue-600 rounded-full flex items-center justify-center">
                  <span className="text-white text-xl">👩‍🚀</span>
                </div>
                <div>
                  <div className="font-bold text-gray-900">Sarah Kim</div>
                  <div className="text-sm text-gray-600">Head of Product</div>
                  <div className="text-xs text-gray-500">LearnTech</div>
                </div>
              </div>
              <div className="bg-blue-50 border border-blue-200 rounded-lg px-3 py-2">
                <span className="text-xs font-semibold text-blue-700">+55% course completion</span>
              </div>
            </div>

            <div className="card hover:shadow-lg transition-all duration-300">
              <blockquote className="text-gray-600 mb-4 leading-relaxed italic">
                "Implementamos en 3 días lo que nos hubiera tomado 8 meses con equipo interno. ROI inmediato."
              </blockquote>
              <div className="flex items-center gap-3 mb-3">
                <div className="w-12 h-12 bg-gradient-to-r from-green-400 to-green-600 rounded-full flex items-center justify-center">
                  <span className="text-white text-xl">👨‍💼</span>
                </div>
                <div>
                  <div className="font-bold text-gray-900">Marcus Weber</div>
                  <div className="text-sm text-gray-600">CEO</div>
                  <div className="text-xs text-gray-500">DevTools Pro</div>
                </div>
              </div>
              <div className="bg-orange-50 border border-orange-200 rounded-lg px-3 py-2">
                <span className="text-xs font-semibold text-orange-700">3 días implementación</span>
              </div>
            </div>
          </div>

          {/* Tipos de SaaS */}
          <div className="text-center">
            <p className="text-sm font-semibold text-purple-600 mb-6">FUNCIONA EN CUALQUIER VERTICAL SaaS</p>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6 items-center justify-items-center opacity-60 hover:opacity-80 transition-opacity">
              <div className="flex flex-col items-center justify-center p-3 bg-white border border-gray-200 rounded-lg hover:shadow-md transition-all group">
                <span className="text-2xl mb-1 group-hover:scale-110 transition-transform">📊</span>
                <span className="text-xs font-medium text-gray-600 text-center">Analytics</span>
              </div>
              <div className="flex flex-col items-center justify-center p-3 bg-white border border-gray-200 rounded-lg hover:shadow-md transition-all group">
                <span className="text-2xl mb-1 group-hover:scale-110 transition-transform">🎯</span>
                <span className="text-xs font-medium text-gray-600 text-center">Marketing</span>
              </div>
              <div className="flex flex-col items-center justify-center p-3 bg-white border border-gray-200 rounded-lg hover:shadow-md transition-all group">
                <span className="text-2xl mb-1 group-hover:scale-110 transition-transform">💼</span>
                <span className="text-xs font-medium text-gray-600 text-center">CRM</span>
              </div>
              <div className="flex flex-col items-center justify-center p-3 bg-white border border-gray-200 rounded-lg hover:shadow-md transition-all group">
                <span className="text-2xl mb-1 group-hover:scale-110 transition-transform">🛠️</span>
                <span className="text-xs font-medium text-gray-600 text-center">Dev Tools</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Integración Técnica */}
      <section className="py-14 md:py-16">
        <div className="max-w-7xl mx-auto px-6">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Integración técnica para SaaS</h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Cómo agregar IA a tu producto en menos de 1 semana
            </p>
          </div>

          <div className="card max-w-6xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Timeline de implementación */}
              <div>
                <h4 className="font-bold text-lg mb-4">Timeline de Implementación</h4>
                <div className="space-y-4">
                  <div className="flex items-center gap-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
                    <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold">1</div>
                    <div>
                      <div className="font-semibold text-gray-900">Día 1-2: Setup & Data</div>
                      <div className="text-sm text-gray-600">Configurar API keys, enviar datos históricos</div>
                    </div>
                  </div>

                  <div className="flex items-center gap-4 p-4 bg-purple-50 rounded-lg border border-purple-200">
                    <div className="w-8 h-8 bg-purple-600 text-white rounded-full flex items-center justify-center text-sm font-bold">2</div>
                    <div>
                      <div className="font-semibold text-gray-900">Día 3-4: Integración</div>
                      <div className="text-sm text-gray-600">Implementar llamadas API en tu frontend</div>
                    </div>
                  </div>

                  <div className="flex items-center gap-4 p-4 bg-green-50 rounded-lg border border-green-200">
                    <div className="w-8 h-8 bg-green-600 text-white rounded-full flex items-center justify-center text-sm font-bold">3</div>
                    <div>
                      <div className="font-semibold text-gray-900">Día 5-7: Testing & Launch</div>
                      <div className="text-sm text-gray-600">A/B testing, ajustes finales, go live</div>
                    </div>
                  </div>
                </div>

                <div className="mt-6 space-y-3">
                  <div className="flex items-center gap-3">
                    <span className="w-5 h-5 text-green-600">✓</span>
                    <span className="text-sm">Sin cambios en tu arquitectura</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <span className="w-5 h-5 text-green-600">✓</span>
                    <span className="text-sm">Compatible con cualquier tech stack</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <span className="w-5 h-5 text-green-600">✓</span>
                    <span className="text-sm">Soporte técnico durante integración</span>
                  </div>
                </div>
              </div>

              {/* Ejemplo de código */}
              <div>
                <h4 className="font-bold text-lg mb-4">Ejemplo de Integración</h4>
                <div className="space-y-4">
                  <div>
                    <h5 className="text-sm font-semibold mb-2 text-gray-600">REACT COMPONENT</h5>
                    <div className="bg-gray-900 rounded-lg p-4 text-sm font-mono text-gray-100 overflow-x-auto">
                      <pre>{`function RecommendationWidget({ userId }) {
  const [recs, setRecs] = useState([]);

  useEffect(() => {
    fetch('/api/recommendations', {
      method: 'POST',
      body: JSON.stringify({
        userId,
        context: 'dashboard',
        limit: 5
      })
    })
    .then(res => res.json())
    .then(data => setRecs(data.recommendations));
  }, [userId]);

  return (
    <div className="recommendations">
      <h3>Recomendado para ti</h3>
      {recs.map(rec => (
        <RecommendationCard key={rec.id} {...rec} />
      ))}
    </div>
  );
}`}</pre>
                    </div>
                  </div>
                </div>

                <div className="mt-6 bg-green-50 border border-green-200 rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="text-green-600">⚡</span>
                    <span className="font-bold text-green-700">Esfuerzo de desarrollo</span>
                  </div>
                  <div className="text-sm text-gray-600 space-y-1">
                    <div>• <strong>Frontend:</strong> 1-2 días (componentes UI)</div>
                    <div>• <strong>Backend:</strong> 1 día (proxy API calls)</div>
                    <div>• <strong>Testing:</strong> 1-2 días (A/B testing)</div>
                    <div className="pt-2 border-t border-green-200">
                      <strong className="text-green-700">Total: 1 developer, 1 semana</strong>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Final */}
      <section className="py-14 md:py-16 section-gradient-strong">
        <div className="max-w-4xl mx-auto px-6 text-center">
          <h2 className="text-3xl font-bold mb-4">
            ¿Listo para agregar IA a tu SaaS como DataFlow?
          </h2>
          <p className="text-lg text-gray-600 mb-8 max-w-2xl mx-auto">
            Únete a decenas de startups SaaS que ya están compitiendo con IA 
            sin equipos de ML internos.
          </p>
          <div className="flex flex-wrap gap-4 justify-center">
            <Link href="/register?utm_source=saas-b2b-final" className="btn-primary">
              Empezar gratis ahora
            </Link>
            <Link href="/contact-sales?utm_source=saas-b2b-final&industry=saas" className="btn-secondary">
              Hablar con experto en SaaS
            </Link>
          </div>
          <p className="text-sm text-gray-500 mt-6">
            Sin compromiso • MVP con IA en 1 semana • Soporte técnico incluido
          </p>
        </div>
      </section>
    </main>
  );
}
