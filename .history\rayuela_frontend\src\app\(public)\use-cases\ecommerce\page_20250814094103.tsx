import React from "react";
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import Link from "next/link";
import { generateMetadata as generateSEOMetadata } from '@/lib/seo';
import { ArrowLeft, TrendingUp, ShoppingCart, Users, Target, CheckCircle, ArrowRight } from "lucide-react";

export const metadata = generateSEOMetadata({
  title: 'E-commerce D2C - Casos de Uso | Rayuela',
  description: 'Descubre cómo Rayuela aumenta el ticket promedio y la conversión en tiendas online D2C. Casos reales, métricas y ROI comprobado.',
  path: '/use-cases/ecommerce',
  keywords: ['ecommerce', 'D2C', 'recomendaciones', 'AOV', 'conversión', 'personalización'],
});

export default function EcommerceUseCasePage() {
  return (
    <main className="bg-white">
      {/* Breadcrumb */}
      <section className="py-6 border-b border-gray-100">
        <div className="max-w-7xl mx-auto px-6">
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <Link href="/" className="hover:text-purple-600 transition-colors flex items-center gap-1">
              <ArrowLeft className="w-4 h-4" />
              Inicio
            </Link>
            <span>/</span>
            <span className="text-gray-900 font-medium">E-commerce</span>
          </div>
        </div>
      </section>

      {/* Hero Section */}
      <section className="py-14 md:py-16">
        <div className="max-w-7xl mx-auto px-6">
          <div className="text-center mb-12">
            <div className="inline-flex items-center gap-2 bg-purple-50 text-purple-700 px-4 py-2 rounded-full text-sm font-medium mb-6">
              <span>🛍️</span>
              Caso de Uso: E-commerce
            </div>
            <h1 className="text-4xl md:text-5xl font-bold leading-tight mb-6">
              Aumenta tu ticket promedio con recomendaciones inteligentes
            </h1>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto mb-8">
              Transforma tu tienda online en una experiencia personalizada que impulsa las ventas y fideliza clientes.
            </p>
          </div>

          {/* Historia de Éxito */}
          <div className="card max-w-5xl mx-auto">
            <div className="flex items-start gap-4">
              <div className="w-16 h-16 bg-gradient-to-r from-purple-600 to-purple-700 rounded-xl flex items-center justify-center flex-shrink-0">
                <span className="text-white text-2xl">🏆</span>
              </div>
              <div className="flex-1">
                <h3 className="text-xl font-bold text-gray-900 mb-3">
                  Caso de Éxito: ModaStyle aumentó ventas 30% en 60 días
                </h3>
                <p className="text-gray-600 mb-6 leading-relaxed">
                  <strong>ModaStyle</strong>, una tienda de moda online con 50,000 SKUs y 200,000 visitantes mensuales,
                  implementó Rayuela en su homepage, páginas de producto y emails. En solo 60 días:
                </p>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                  <div className="bg-green-50 border border-green-200 rounded-lg p-4 text-center">
                    <div className="text-2xl font-bold text-green-600 mb-1">+30%</div>
                    <div className="text-sm text-gray-600">Ventas totales</div>
                  </div>
                  <div className="bg-purple-50 border border-purple-200 rounded-lg p-4 text-center">
                    <div className="text-2xl font-bold text-purple-600 mb-1">+45€</div>
                    <div className="text-sm text-gray-600">AOV promedio</div>
                  </div>
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 text-center">
                    <div className="text-2xl font-bold text-blue-600 mb-1">+28%</div>
                    <div className="text-sm text-gray-600">Repeat purchases</div>
                  </div>
                </div>
                <blockquote className="pl-4 border-l-4 border-purple-200 italic text-gray-600 bg-gray-50 p-4 rounded-r-lg">
                  "Implementamos Rayuela en 2 días. El primer mes ya vimos el ROI completo.
                  Nuestros clientes ahora descubren productos que ni sabían que teníamos."
                  <footer className="mt-2 text-sm not-italic">
                    — <strong className="text-gray-900">María González</strong>, CTO de ModaStyle
                  </footer>
                </blockquote>
              </div>
            </div>
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid gap-6 md:grid-cols-3 mb-12">
          <Card className="text-center">
            <CardContent className="pt-6">
              <TrendingUp className="w-12 h-12 text-success mx-auto mb-4" />
              <div className="text-3xl font-bold text-success mb-2">+15%</div>
              <p className="text-muted-foreground">Aumento en AOV (Average Order Value)</p>
            </CardContent>
          </Card>
          <Card className="text-center">
            <CardContent className="pt-6">
              <Target className="w-12 h-12 text-primary mx-auto mb-4" />
              <div className="text-3xl font-bold text-primary mb-2">+3pp</div>
              <p className="text-muted-foreground">Mejora en tasa de conversión</p>
            </CardContent>
          </Card>
          <Card className="text-center">
            <CardContent className="pt-6">
              <Users className="w-12 h-12 text-warning mx-auto mb-4" />
              <div className="text-3xl font-bold text-warning mb-2">+28%</div>
              <p className="text-muted-foreground">Incremento en repeat purchases</p>
            </CardContent>
          </Card>
        </div>

        {/* Diagrama de Implementación */}
        <Card className="mb-12">
          <CardHeader>
            <CardTitle>Dónde colocar las recomendaciones para máximo impacto</CardTitle>
            <CardDescription>
              Puntos estratégicos que generan más conversión y AOV
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Diagrama Visual */}
              <div className="space-y-6">
                <h4 className="font-semibold text-lg mb-4">Flujo del Cliente</h4>

                {[
                  {
                    step: '1',
                    title: 'Homepage',
                    description: '"Productos recomendados para ti"',
                    impact: '+12% CTR vs productos genéricos',
                    color: 'from-blue-500 to-cyan-500'
                  },
                  {
                    step: '2',
                    title: 'Página de Producto',
                    description: '"Otros clientes también compraron"',
                    impact: '+25% add-to-cart rate',
                    color: 'from-green-500 to-emerald-500'
                  },
                  {
                    step: '3',
                    title: 'Carrito de Compras',
                    description: '"Completa tu look con estos productos"',
                    impact: '+18% AOV promedio',
                    color: 'from-purple-500 to-violet-500'
                  },
                  {
                    step: '4',
                    title: 'Email Marketing',
                    description: '"Basándote en tu última compra"',
                    impact: '+35% open rate, +22% click rate',
                    color: 'from-orange-500 to-red-500'
                  }
                ].map((item) => (
                  <div key={item.step} className="flex items-start gap-4">
                    <div className={`w-10 h-10 bg-gradient-to-r ${item.color} rounded-full flex items-center justify-center text-white font-bold flex-shrink-0`}>
                      {item.step}
                    </div>
                    <div className="flex-1">
                      <h5 className="font-semibold text-foreground mb-1">{item.title}</h5>
                      <p className="text-sm text-muted-foreground mb-2">{item.description}</p>
                      <div className="bg-success/10 border border-success/20 rounded-lg px-3 py-1 inline-block">
                        <span className="text-xs font-medium text-success">{item.impact}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Mockup Visual */}
              <div className="bg-slate-50 dark:bg-slate-800 rounded-xl p-6">
                <h4 className="font-semibold text-lg mb-4">Ejemplo Visual</h4>
                <div className="space-y-4">
                  <div className="bg-white dark:bg-slate-700 rounded-lg p-4 border border-slate-200 dark:border-slate-600">
                    <div className="text-sm font-medium text-foreground mb-2">🏠 Homepage</div>
                    <div className="text-xs text-muted-foreground">"Recomendado para ti"</div>
                    <div className="flex gap-2 mt-2">
                      {['👕', '👖', '👟'].map((item) => (
                        <div key={item} className="w-8 h-8 bg-primary/10 rounded flex items-center justify-center text-xs">
                          {item}
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="bg-white dark:bg-slate-700 rounded-lg p-4 border border-slate-200 dark:border-slate-600">
                    <div className="text-sm font-medium text-foreground mb-2">📱 Producto</div>
                    <div className="text-xs text-muted-foreground">"Otros también compraron"</div>
                    <div className="flex gap-2 mt-2">
                      {['🧥', '👜', '⌚'].map((item) => (
                        <div key={item} className="w-8 h-8 bg-accent/10 rounded flex items-center justify-center text-xs">
                          {item}
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="bg-white dark:bg-slate-700 rounded-lg p-4 border border-slate-200 dark:border-slate-600">
                    <div className="text-sm font-medium text-foreground mb-2">🛒 Carrito</div>
                    <div className="text-xs text-muted-foreground">"Completa tu look"</div>
                    <div className="flex gap-2 mt-2">
                      {['👓', '💍', '👠'].map((item) => (
                        <div key={item} className="w-8 h-8 bg-warning/10 rounded flex items-center justify-center text-xs">
                          {item}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Problem & Solution */}
        <div className="grid gap-8 md:grid-cols-2 mb-12">
          <Card className="border-destructive/20">
            <CardHeader>
              <CardTitle className="text-destructive">El Problema</CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-3 text-muted-foreground">
                <li>• <strong>Productos genéricos:</strong> Todos los usuarios ven las mismas recomendaciones</li>
                <li>• <strong>Oportunidades perdidas:</strong> 70% de los visitantes se van sin comprar</li>
                <li>• <strong>Cross-selling limitado:</strong> Difícil identificar productos complementarios</li>
                <li>• <strong>Competencia feroz:</strong> Clientes comparan precios en múltiples tiendas</li>
                <li>• <strong>Costos de adquisición altos:</strong> CAC creciente en canales digitales</li>
              </ul>
            </CardContent>
          </Card>

          <Card className="border-success/20">
            <CardHeader>
              <CardTitle className="text-success">La Solución Rayuela</CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-3 text-muted-foreground">
                <li>• <strong>Personalización 1:1:</strong> Cada usuario ve productos relevantes para él</li>
                <li>• <strong>Recomendaciones contextuales:</strong> Basadas en comportamiento y preferencias</li>
                <li>• <strong>Cross-selling inteligente:</strong> Productos complementarios automáticos</li>
                <li>• <strong>Experiencia diferenciada:</strong> Ventaja competitiva sostenible</li>
                <li>• <strong>Mayor LTV:</strong> Clientes más satisfechos compran más veces</li>
              </ul>
            </CardContent>
          </Card>
        </div>

        {/* Implementation Scenarios */}
        <Card className="mb-12">
          <CardHeader>
            <CardTitle>Escenarios de Implementación</CardTitle>
            <CardDescription>
              Cómo Rayuela se integra en diferentes puntos de tu customer journey
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-6 md:grid-cols-2">
              <div className="space-y-4">
                <h4 className="font-semibold text-lg">Homepage Personalizada</h4>
                <p className="text-sm text-muted-foreground">
                  Muestra productos relevantes desde el primer momento basándote en el historial del usuario, 
                  ubicación geográfica y tendencias de temporada.
                </p>
                <div className="bg-slate-50 dark:bg-slate-800 p-4 rounded-lg">
                  <p className="text-sm font-mono">
                    "Usuarios que regresan ven 40% más productos de su interés"
                  </p>
                </div>
              </div>

              <div className="space-y-4">
                <h4 className="font-semibold text-lg">Página de Producto</h4>
                <p className="text-sm text-muted-foreground">
                  Sugiere productos complementarios, accesorios y alternativas que realmente interesan 
                  al usuario específico.
                </p>
                <div className="bg-slate-50 dark:bg-slate-800 p-4 rounded-lg">
                  <p className="text-sm font-mono">
                    "Cross-selling efectivo en 65% de las sesiones"
                  </p>
                </div>
              </div>

              <div className="space-y-4">
                <h4 className="font-semibold text-lg">Carrito de Compras</h4>
                <p className="text-sm text-muted-foreground">
                  Recomienda productos que completan la compra o aprovecha el momento de alta intención 
                  para sugerir upgrades.
                </p>
                <div className="bg-slate-50 dark:bg-slate-800 p-4 rounded-lg">
                  <p className="text-sm font-mono">
                    "Incremento promedio de $45 por orden"
                  </p>
                </div>
              </div>

              <div className="space-y-4">
                <h4 className="font-semibold text-lg">Email Marketing</h4>
                <p className="text-sm text-muted-foreground">
                  Personaliza campañas de email con productos específicos para cada segmento de usuarios, 
                  aumentando el engagement.
                </p>
                <div className="bg-slate-50 dark:bg-slate-800 p-4 rounded-lg">
                  <p className="text-sm font-mono">
                    "CTR 3x superior vs. emails genéricos"
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* ROI Calculator */}
        <Card className="mb-12">
          <CardHeader>
            <CardTitle>Calculadora de ROI</CardTitle>
            <CardDescription>
              Estima el impacto financiero de implementar Rayuela en tu e-commerce
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-6 md:grid-cols-2">
              <div className="space-y-4">
                <h4 className="font-semibold">Ejemplo: Tienda con 10,000 visitantes/mes</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Visitantes mensuales:</span>
                    <span className="font-mono">10,000</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Conversión actual (2%):</span>
                    <span className="font-mono">200 órdenes</span>
                  </div>
                  <div className="flex justify-between">
                    <span>AOV actual:</span>
                    <span className="font-mono">$75</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Revenue mensual actual:</span>
                    <span className="font-mono">$15,000</span>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <h4 className="font-semibold text-success">Con Rayuela:</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Conversión mejorada (2.6%):</span>
                    <span className="font-mono text-success">260 órdenes</span>
                  </div>
                  <div className="flex justify-between">
                    <span>AOV mejorado:</span>
                    <span className="font-mono text-success">$86</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Revenue mensual nuevo:</span>
                    <span className="font-mono text-success">$22,360</span>
                  </div>
                  <div className="flex justify-between border-t pt-2">
                    <span className="font-semibold">Incremento mensual:</span>
                    <span className="font-mono font-bold text-success">+$7,360</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="font-semibold">ROI anual:</span>
                    <span className="font-mono font-bold text-success">+$88,320</span>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Technical Benefits */}
        <Card className="mb-12">
          <CardHeader>
            <CardTitle>Beneficios Técnicos para tu Equipo</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2">
              <div className="flex items-start gap-3">
                <CheckCircle className="w-5 h-5 text-success mt-1" />
                <div>
                  <h4 className="font-semibold">Integración en 1 día</h4>
                  <p className="text-sm text-muted-foreground">
                    API REST simple, SDKs para Python/Node.js, documentación completa
                  </p>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <CheckCircle className="w-5 h-5 text-success mt-1" />
                <div>
                  <h4 className="font-semibold">Sin infraestructura ML</h4>
                  <p className="text-sm text-muted-foreground">
                    No necesitas data scientists ni servidores de ML. Todo en la nube
                  </p>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <CheckCircle className="w-5 h-5 text-success mt-1" />
                <div>
                  <h4 className="font-semibold">Escalabilidad automática</h4>
                  <p className="text-sm text-muted-foreground">
                    Desde 1,000 hasta 10M+ requests/mes sin cambios en tu código
                  </p>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <CheckCircle className="w-5 h-5 text-success mt-1" />
                <div>
                  <h4 className="font-semibold">Analytics en tiempo real</h4>
                  <p className="text-sm text-muted-foreground">
                    Dashboard con métricas de performance y A/B testing integrado
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Explicación Técnica para CTOs */}
        <Card className="mb-12">
          <CardHeader>
            <CardTitle>Integración técnica simplificada</CardTitle>
            <CardDescription>
              Para CTOs evaluando el esfuerzo de implementación
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Esquema de Arquitectura */}
              <div>
                <h4 className="font-semibold text-lg mb-4">Arquitectura de Integración</h4>
                <div className="bg-slate-50 dark:bg-slate-800 rounded-xl p-6">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="bg-blue-100 dark:bg-blue-900 rounded-lg p-3 text-center flex-1 mr-2">
                        <div className="text-sm font-medium">Tu E-commerce</div>
                        <div className="text-xs text-muted-foreground">Frontend + Backend</div>
                      </div>
                      <div className="text-primary">→</div>
                      <div className="bg-primary/10 rounded-lg p-3 text-center flex-1 ml-2">
                        <div className="text-sm font-medium">Rayuela API</div>
                        <div className="text-xs text-muted-foreground">REST + JSON</div>
                      </div>
                    </div>

                    <div className="text-center text-sm text-muted-foreground">
                      <div className="mb-2">Flujo de datos:</div>
                      <div className="space-y-1 text-xs">
                        <div>1. Envías productos/usuarios/interacciones</div>
                        <div>2. Rayuela entrena modelos automáticamente</div>
                        <div>3. Solicitas recomendaciones via API</div>
                        <div>4. Recibes productos personalizados</div>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="mt-6 space-y-3">
                  <div className="flex items-center gap-3">
                    <CheckCircle className="w-5 h-5 text-success" />
                    <span className="text-sm">Sin cambios en tu base de datos</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <CheckCircle className="w-5 h-5 text-success" />
                    <span className="text-sm">API REST estándar (JSON)</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <CheckCircle className="w-5 h-5 text-success" />
                    <span className="text-sm">Latencia &lt;100ms garantizada</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <CheckCircle className="w-5 h-5 text-success" />
                    <span className="text-sm">Escalabilidad automática</span>
                  </div>
                </div>
              </div>

              {/* Ejemplo JSON */}
              <div>
                <h4 className="font-semibold text-lg mb-4">Ejemplo Request/Response</h4>
                <div className="space-y-4">
                  <div>
                    <h5 className="text-sm font-medium mb-2 text-muted-foreground">REQUEST</h5>
                    <div className="bg-slate-800 rounded-lg p-4 text-sm font-mono text-slate-100 overflow-x-auto">
                      <pre>{`POST /api/v1/recommendations
{
  "userId": "user_12345",
  "context": {
    "page": "product",
    "productId": "prod_789"
  },
  "limit": 5,
  "filters": {
    "category": "fashion",
    "inStock": true
  }
}`}</pre>
                    </div>
                  </div>

                  <div>
                    <h5 className="text-sm font-medium mb-2 text-muted-foreground">RESPONSE</h5>
                    <div className="bg-slate-800 rounded-lg p-4 text-sm font-mono text-slate-100 overflow-x-auto">
                      <pre>{`{
  "recommendations": [
    {
      "productId": "prod_456",
      "score": 0.95,
      "reason": "frequently_bought_together"
    },
    {
      "productId": "prod_123",
      "score": 0.89,
      "reason": "similar_users_liked"
    }
  ],
  "responseTime": "45ms"
}`}</pre>
                    </div>
                  </div>
                </div>

                <div className="mt-6 bg-success/10 border border-success/20 rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="text-success">⚡</span>
                    <span className="font-semibold text-success">Tiempo de implementación</span>
                  </div>
                  <div className="text-sm text-muted-foreground space-y-1">
                    <div>• <strong>Setup inicial:</strong> 2-4 horas</div>
                    <div>• <strong>Integración frontend:</strong> 4-6 horas</div>
                    <div>• <strong>Testing y ajustes:</strong> 2-3 horas</div>
                    <div className="pt-2 border-t border-success/20">
                      <strong className="text-success">Total: 1-2 días de desarrollo</strong>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Pruebas Sociales - E-commerce */}
        <Card className="mb-12">
          <CardHeader>
            <CardTitle>Lo que dicen nuestros clientes de e-commerce</CardTitle>
            <CardDescription>
              Testimonios reales de tiendas online que ya están usando Rayuela
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
              {[
                {
                  quote: "Implementamos Rayuela en 2 días y vimos ROI inmediato. Nuestro AOV subió 15% el primer mes sin cambiar nada más.",
                  author: "María González",
                  role: "CTO",
                  company: "ModaStyle",
                  avatar: "👩‍💼",
                  metrics: "+15% AOV, +30% ventas"
                },
                {
                  quote: "Las recomendaciones en carrito aumentaron nuestro ticket promedio €45. Es como tener un vendedor experto 24/7.",
                  author: "Carlos Mendoza",
                  role: "E-commerce Manager",
                  company: "TechGadgets",
                  avatar: "👨‍💻",
                  metrics: "+€45 AOV, +22% conversión"
                },
                {
                  quote: "Nuestros clientes ahora descubren productos que ni sabían que teníamos. Las ventas cruzadas se dispararon.",
                  author: "Ana Rodríguez",
                  role: "Head of Digital",
                  company: "HomeDecor Plus",
                  avatar: "👩‍🚀",
                  metrics: "+35% cross-selling, +28% repeat"
                }
              ].map((testimonial) => (
                <div
                  key={testimonial.author}
                  className="bg-card border border-border rounded-xl p-6 hover:shadow-lg transition-all duration-300"
                >
                  <blockquote className="text-muted-foreground mb-4 leading-relaxed italic">
                    "{testimonial.quote}"
                  </blockquote>
                  <div className="flex items-center gap-3 mb-3">
                    <div className="w-12 h-12 bg-gradient-to-r from-primary/20 to-accent/20 rounded-full flex items-center justify-center">
                      <span className="text-xl">{testimonial.avatar}</span>
                    </div>
                    <div>
                      <div className="font-semibold text-foreground">{testimonial.author}</div>
                      <div className="text-sm text-muted-foreground">{testimonial.role}</div>
                      <div className="text-xs text-muted-foreground/70">{testimonial.company}</div>
                    </div>
                  </div>
                  <div className="bg-success/10 border border-success/20 rounded-lg px-3 py-2">
                    <span className="text-xs font-medium text-success">{testimonial.metrics}</span>
                  </div>
                </div>
              ))}
            </div>

            {/* Logos de Clientes E-commerce */}
            <div className="text-center mb-6">
              <p className="text-sm font-semibold text-primary mb-4">CONFÍAN EN RAYUELA</p>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-6 items-center justify-items-center opacity-60 hover:opacity-80 transition-opacity">
                {[
                  { name: 'Shopify Plus', icon: '🛍️' },
                  { name: 'WooCommerce', icon: '🛒' },
                  { name: 'Magento', icon: '🏪' },
                  { name: 'PrestaShop', icon: '🏬' },
                ].map((platform) => (
                  <div
                    key={platform.name}
                    className="flex flex-col items-center justify-center p-3 bg-card border border-border rounded-lg hover:shadow-md transition-all group"
                  >
                    <span className="text-2xl mb-1 group-hover:scale-110 transition-transform">{platform.icon}</span>
                    <span className="text-xs font-medium text-muted-foreground text-center">{platform.name}</span>
                  </div>
                ))}
              </div>
            </div>

            <div className="text-center">
              <div className="inline-flex items-center gap-4 bg-primary/10 border border-primary/20 rounded-full px-6 py-3">
                <span className="text-primary text-lg">✓</span>
                <span className="text-primary font-semibold">Más de 500 tiendas online ya usan Rayuela</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* CTA Contextual para E-commerce */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
          {/* CTA Técnico */}
          <Card className="border-primary/20 bg-gradient-to-br from-primary/5 to-primary/10">
            <CardContent className="pt-8">
              <div className="text-center">
                <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">🚀</span>
                </div>
                <h3 className="text-xl font-bold mb-3">
                  Para Developers & CTOs
                </h3>
                <p className="text-muted-foreground mb-6">
                  Prueba la integración con tu stack actual.
                  Sandbox completo y documentación técnica.
                </p>
                <Button asChild size="lg" className="w-full">
                  <Link href="/register?utm_source=ecommerce-tech">
                    Empezar integración técnica
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Link>
                </Button>
                <p className="text-xs text-muted-foreground mt-3">
                  API keys gratis • Sandbox ilimitado • Soporte técnico
                </p>
              </div>
            </CardContent>
          </Card>

          {/* CTA Comercial */}
          <Card className="border-accent/20 bg-gradient-to-br from-accent/5 to-accent/10">
            <CardContent className="pt-8">
              <div className="text-center">
                <div className="w-16 h-16 bg-accent/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">💼</span>
                </div>
                <h3 className="text-xl font-bold mb-3">
                  Para Product Owners & CMOs
                </h3>
                <p className="text-muted-foreground mb-6">
                  Ve una demo personalizada con datos de tu industria.
                  Calculamos tu ROI potencial.
                </p>
                <Button asChild variant="outline" size="lg" className="w-full border-accent/30 hover:bg-accent/5">
                  <Link href="/contact-sales?utm_source=ecommerce-business&industry=ecommerce">
                    Solicitar demo de e-commerce
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Link>
                </Button>
                <p className="text-xs text-muted-foreground mt-3">
                  Demo en 30 min • ROI calculado • Casos similares
                </p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* CTA Final Unificado */}
        <Card className="bg-gradient-to-r from-primary/10 to-accent/10 border-primary/20">
          <CardContent className="pt-8">
            <div className="text-center">
              <h3 className="text-2xl font-bold mb-4">
                ¿Listo para aumentar tu AOV como ModaStyle?
              </h3>
              <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
                Únete a cientos de tiendas online que ya están aumentando sus ventas
                con recomendaciones inteligentes de Rayuela.
              </p>
              <div className="flex gap-4 justify-center flex-wrap">
                <Button asChild size="lg">
                  <Link href="/register?utm_source=ecommerce-final">
                    Empezar gratis ahora
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Link>
                </Button>
                <Button variant="outline" asChild size="lg">
                  <Link href="/contact-sales?utm_source=ecommerce-final&industry=ecommerce">
                    Hablar con experto en e-commerce
                  </Link>
                </Button>
              </div>
              <p className="text-xs text-muted-foreground mt-4">
                Sin compromiso • Integración en 48 horas • ROI visible en 30 días
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
