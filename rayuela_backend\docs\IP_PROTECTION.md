# 🔒 Protección de Propiedad Intelectual (IP) - Rayuela API

## Resumen Ejecutivo

Este documento describe las medidas implementadas para proteger la propiedad intelectual de Rayuela, específicamente los algoritmos de recomendación y métricas de machine learning, mientras se mantiene una API pública funcional para desarrolladores externos.

## 🎯 Objetivos de Seguridad

1. **Protección de Algoritmos**: Ocultar detalles de implementación de algoritmos de recomendación
2. **Ocultación de Métricas**: Proteger métricas propietarias de evaluación de ML
3. **Acceso Controlado**: Mantener versión completa para usuarios autenticados
4. **Monitoreo de Seguridad**: Registrar todos los accesos a información sensible

## 🏗️ Arquitectura de Protección

### Doble Endpoint OpenAPI

```
┌─────────────────────────────────────────────────────────────┐
│                    Rayuela API                              │
├─────────────────────────────────────────────────────────────┤
│  📖 /api/v1/public-openapi.json                           │
│  ├─ Sin autenticación requerida                            │
│  ├─ Información filtrada y sanitizada                      │
│  ├─ Términos técnicos reemplazados                         │
│  └─ Endpoints públicos únicamente                          │
├─────────────────────────────────────────────────────────────┤
│  🔒 /api/v1/openapi.json                                  │
│  ├─ Autenticación requerida                                │
│  ├─ Información técnica completa                           │
│  ├─ Todos los endpoints disponibles                        │
│  └─ Monitoreo de seguridad activo                          │
└─────────────────────────────────────────────────────────────┘
```

### Esquemas Públicos vs Privados

#### Esquemas Privados (Internos)
```python
class RecommendationGoal(str, Enum):
    MAXIMIZE_ENGAGEMENT = "maximize_engagement"
    DISCOVER_DIVERSE = "discover_diverse"
    PROMOTE_NEW_ARRIVALS = "promote_new_arrivals"
    BALANCED = "balanced"

class ModelVariant(str, Enum):
    COLLABORATIVE = "collaborative"
    CONTENT_BASED = "content_based"
    HYBRID = "hybrid"
```

#### Esquemas Públicos (Expuestos)
```python
class PublicRecommendationGoal(str, Enum):
    ENGAGEMENT = "engagement"
    DISCOVERY = "discovery"
    FRESHNESS = "freshness"
    BALANCED = "balanced"

class PublicModelVariant(str, Enum):
    STANDARD = "standard"
    ADVANCED = "advanced"
    PREMIUM = "premium"
```

## 🛡️ Medidas de Protección Implementadas

### 1. Filtrado de Términos Sensibles

**Términos Protegidos:**
- `collaborative`, `content-based`, `hybrid` → Algoritmos de ML
- `precision`, `recall`, `NDCG`, `MAP` → Métricas de evaluación
- `model_type`, `strategy`, `algorithm` → Parámetros técnicos
- `catalog_coverage`, `diversity`, `novelty` → Métricas avanzadas

**Proceso de Limpieza:**
```python
def clean_endpoint_details(endpoint_details: Dict[str, Any]) -> Dict[str, Any]:
    # Reemplazar términos sensibles con genéricos
    # Filtrar parámetros técnicos
    # Sanitizar ejemplos de requests/responses
    # Remover descripciones detalladas
```

### 2. Mapeo de Conversión

**Conversión Automática:**
```python
# Interno → Público
GOAL_MAPPING = {
    "maximize_engagement": "engagement",
    "discover_diverse": "discovery",
    "promote_new_arrivals": "freshness",
    "balanced": "balanced"
}

MODEL_MAPPING = {
    "collaborative": "standard",
    "content_based": "advanced", 
    "hybrid": "premium"
}
```

### 3. Monitoreo de Seguridad

**Logs de Acceso Público:**
```json
{
  "message": "🌐 PUBLIC OpenAPI Access",
  "client_ip": "*************",
  "user_agent": "PostmanRuntime/7.28.0",
  "endpoint": "/api/v1/public-openapi.json",
  "security_event": "public_openapi_access",
  "critical_action": false,
  "alert_level": "INFO"
}
```

**Logs de Acceso Privado:**
```json
{
  "message": "🔒 PRIVATE OpenAPI Access - SECURITY ALERT",
  "account_id": 12345,
  "account_name": "Acme Corp",
  "client_ip": "*************",
  "endpoint": "/api/v1/openapi.json",
  "security_event": "private_openapi_access",
  "critical_action": true,
  "alert_level": "HIGH"
}
```

## 🧪 Testing y Validación

### Suite de Tests Completa

**Tests de Protección:**
- ✅ Acceso público sin autenticación
- ✅ Acceso privado requiere autenticación
- ✅ Filtrado de términos sensibles
- ✅ Presencia de términos públicos seguros
- ✅ Estructura de esquemas públicos
- ✅ Validación de endpoints filtrados

**Tests de Monitoreo:**
- ✅ Logs de acceso público
- ✅ Logs de acceso privado con alertas
- ✅ Información de seguridad completa

**Ejecución:**
```bash
python -m pytest tests/test_ip_protection.py -v
```

## 📊 Métricas de Protección

### Información Protegida

| Categoría | Elementos Protegidos | Método de Protección |
|-----------|---------------------|---------------------|
| Algoritmos | collaborative, content-based, hybrid | Mapeo a términos genéricos |
| Métricas ML | precision, recall, NDCG, MAP | Filtrado completo |
| Parámetros | model_type, strategy, algorithm | Eliminación de parámetros |
| Endpoints | Endpoints internos | Lista blanca de públicos |

### Cobertura de Tests

```
13 tests passed, 0 failed
- 9 tests de protección de IP
- 2 tests de mapeo de conversión  
- 2 tests de monitoreo de seguridad
```

## 🚀 Uso para Desarrolladores

### Acceso Público (Recomendado para Terceros)

```bash
curl -X GET "https://api.rayuela.com/api/v1/public-openapi.json"
```

**Características:**
- Sin autenticación requerida
- Información sanitizada
- Términos técnicos simplificados
- Ideal para integración externa

### Acceso Privado (Solo Usuarios Autenticados)

```bash
curl -X GET "https://api.rayuela.com/api/v1/openapi.json" \
  -H "X-API-Key: your-api-key"
```

**Características:**
- Autenticación requerida
- Información técnica completa
- Todos los endpoints disponibles
- Monitoreo de seguridad activo

## 🔧 Mantenimiento

### Actualización de Términos Protegidos

Para agregar nuevos términos sensibles:

1. Actualizar `SENSITIVE_TERMS` en `tests/test_ip_protection.py`
2. Modificar función `clean_endpoint_details()` en `public_openapi.py`
3. Ejecutar tests para validar protección
4. Actualizar documentación

### Monitoreo Continuo

- Revisar logs de acceso privado regularmente
- Alertas automáticas para accesos sospechosos
- Auditoría mensual de términos expuestos

## 📋 Checklist de Seguridad

- [x] Doble endpoint OpenAPI implementado
- [x] Filtrado de términos sensibles activo
- [x] Mapeo de conversión funcionando
- [x] Monitoreo de seguridad configurado
- [x] Tests de protección pasando
- [x] Documentación actualizada
- [x] Logs de auditoría funcionando

## 🆘 Contacto de Seguridad

Para reportar vulnerabilidades o problemas de seguridad relacionados con la protección de IP:

- **Email**: <EMAIL>
- **Slack**: #security-alerts
- **Escalación**: CTO / Head of Security

---

**Última actualización**: 2025-08-15  
**Versión**: 1.0  
**Responsable**: Equipo de Seguridad Rayuela
