# Python
__pycache__/
*.pyc
*.pyo
*.pyd
*.so
.Python
*.egg-info/
dist/
build/

# Virtual environments
.venv/
venv/
env/

# Environment variables
.env
.env.*
.env.local
.env.production
.env.development
.env.staging

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# IDE/Editor
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Testing
.pytest_cache/
.coverage
htmlcov/
.tox/
.cache
coverage.xml
*.cover
.hypothesis/

# Logs
*.log
logs/

# Database
*.db
*.sqlite3

# Cloud Build local testing
.docker/

# Temporary files
*.tmp
*.temp
*.bak

# Security & Secrets (CRITICAL - Never commit these!)
*secret*
*key*.txt
*password*.txt
*token*.txt
*credential*.txt
*auth*.txt
temp_*.txt
*.pem
*.p12
*.pfx
*.key
*.crt
*.csr
service-account*.json
gcp-*.json
aws-*.json
azure-*.json

# Google Cloud specific
application_default_credentials.json
service_account.json
gcp_credentials.json

# Database connection strings and URLs
*_url.txt
*_connection.txt
database_*.txt
redis_*.txt
postgres_*.txt

# API Keys and tokens
api_key*.txt
access_token*.txt
refresh_token*.txt
jwt_*.txt
