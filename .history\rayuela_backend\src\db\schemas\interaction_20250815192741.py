from pydantic import BaseModel, <PERSON>, model_validator
from datetime import datetime
from typing import Optional, Dict, Any, Union
from src.db.enums import InteractionType
from decimal import Decimal
from .base import CamelCaseModel


class InteractionBase(CamelCaseModel):
    user_id: int
    product_id: int
    interaction_type: InteractionType
    value: Decimal
    recommendation_metadata: Optional[Dict[str, Any]] = None


class InteractionExternalCreate(BaseModel):
    """Schema for creating interactions using external IDs."""
    external_user_id: str = Field(..., description="External user identifier provided by the client")
    external_product_id: str = Field(..., description="External product identifier provided by the client")
    interaction_type: InteractionType
    value: Decimal
    recommendation_metadata: Optional[Dict[str, Any]] = None


class InteractionCreate(CamelCaseModel):
    """Schema for creating interactions with support for both internal and external IDs."""
    # Internal IDs (optional)
    user_id: Optional[int] = Field(None, description="Internal user ID")
    product_id: Optional[int] = Field(None, description="Internal product ID")

    # External IDs (optional)
    external_user_id: Optional[str] = Field(None, description="External user identifier provided by the client")
    external_product_id: Optional[str] = Field(None, description="External product identifier provided by the client")

    # Required fields
    interaction_type: InteractionType
    value: Decimal
    recommendation_metadata: Optional[Dict[str, Any]] = None

    @model_validator(mode='after')
    def validate_id_fields(self):
        """Validate that either internal IDs or external IDs are provided, but not both."""
        has_internal_ids = self.user_id is not None and self.product_id is not None
        has_external_ids = self.external_user_id is not None and self.external_product_id is not None

        if not has_internal_ids and not has_external_ids:
            raise ValueError(
                "Either both internal IDs (user_id, product_id) or both external IDs "
                "(external_user_id, external_product_id) must be provided"
            )

        if has_internal_ids and has_external_ids:
            raise ValueError(
                "Cannot provide both internal IDs and external IDs. "
                "Use either (user_id, product_id) or (external_user_id, external_product_id)"
            )

        # Validate that if one external ID is provided, both must be provided
        if (self.external_user_id is None) != (self.external_product_id is None):
            raise ValueError(
                "Both external_user_id and external_product_id must be provided together"
            )

        # Validate that if one internal ID is provided, both must be provided
        if (self.user_id is None) != (self.product_id is None):
            raise ValueError(
                "Both user_id and product_id must be provided together"
            )

        return self


class Interaction(InteractionBase):
    id: int
    account_id: int
    timestamp: datetime

    class ConfigDict:
        from_attributes = True


class InteractionExternal(BaseModel):
    """Schema for interaction responses that include external IDs."""
    id: int
    account_id: int
    external_user_id: str = Field(..., description="External user identifier")
    external_product_id: str = Field(..., description="External product identifier")
    interaction_type: InteractionType
    value: Decimal
    timestamp: datetime
    recommendation_metadata: Optional[Dict[str, Any]] = None

    class ConfigDict:
        from_attributes = True
