"use client";

import { motion } from "framer-motion";
import Image from "next/image";
import Link from "next/link";
import { useState } from "react";

const fadeUp = {
  hidden: { opacity: 0, y: 32 },
  show: { opacity: 1, y: 0 },
};

export default function HomePage() {
  const [codeExample, setCodeExample] = useState<"python" | "node" | "curl">("python");

  const codeSnippets = {
    python: `# pip install rayuela-sdk
from rayuela import Client

client = Client(api_key="YOUR_API_KEY")
recs = client.get_recommendations(user_id="123", limit=10)
for r in recs:
    print(r["item_id"], r["score"])`,
    node: `npm install rayuela-sdk

const { Client } = require("rayuela");
const client = new Client({ apiKey: "YOUR_API_KEY" });

(async () => {
  const recs = await client.getRecommendations({ userId: "123", limit: 10 });
  console.log(recs);
})();`,
    curl: `curl -X POST "https://api.rayuela.ai/v1/recommendations" \\
-H "Content-Type: application/json" \\
-H "api-key: YOUR_API_KEY" \\
-d '{ "user_id": "123", "limit": 10 }'`,
  };

  const handleCopy = () => {
    navigator.clipboard.writeText(codeSnippets[codeExample]);
  };

  return (
    <main className="bg-white">
      {/* ===== HERO ===== */}
      <section className="py-14 md:py-16">
        <div className="max-w-7xl mx-auto px-6 grid md:grid-cols-2 gap-10 items-center">
          <motion.div
            variants={fadeUp}
            initial="hidden"
            animate="show"
            className="order-2 md:order-1"
          >
            <h1 className="text-4xl md:text-5xl font-bold leading-tight mb-4">
              Personalización con IA para E-commerce y Marketplaces
            </h1>
            <p className="text-lg text-gray-600 mb-6">
              Rayuela es una API-first de recomendaciones: integración en <strong>horas</strong>, impacto en
              <strong> conversión y AOV</strong>, sin MLOps ni complejidad. Diseñada para CTOs, Product Leaders y devs.
            </p>
            <div className="flex flex-wrap gap-4">
              <Link href="/register" className="btn-primary" aria-label="Crear cuenta gratis">
                Comenzar ahora
              </Link>
              <Link
                href="/pricing"
                className="px-5 py-2.5 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-100 transition"
                aria-label="Ver planes y precios"
              >
                Ver planes
              </Link>
            </div>
            <div className="mt-6 text-sm text-gray-500">
              <span className="mr-2">✅ Free sandbox</span>
              <span className="mr-2">✅ API Keys instantáneas</span>
              <span>✅ SDKs para Python y Node</span>
            </div>
          </motion.div>

          <motion.div
            className="order-1 md:order-2 flex justify-center"
            initial={{ opacity: 0, y: 32 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <Image
              src="/dashboard-hero.png"
              alt="Rayuela dashboard: métricas y recomendaciones"
              width={430}
              height={420}
              priority
              className="hero-img"
            />
          </motion.div>
        </div>
      </section>

      {/* ===== DIFERENCIADORES ===== */}
      <section className="py-12 bg-gradient-to-b from-purple-50 to-white">
        <div className="max-w-7xl mx-auto px-6">
          <h2 className="text-2xl font-bold mb-8 text-center">Diferenciadores clave</h2>
          <div className="grid md:grid-cols-3 lg:grid-cols-6 gap-6">
            {[
              ["IA explicable (XAI)", "Entiende por qué se recomienda cada ítem. Transparencia para PMs, confianza para tu negocio."],
              ["Optimizado con LTR", "Learning-to-Rank para maximizar métricas de negocio (conversión, AOV), no solo precisión."],
              ["Cold-start sin fricción", "Fallback inteligente y señales iniciales para mostrar recomendaciones relevantes desde el día uno."],
              ["Multi-tenant seguro (RLS)", "Aislamiento de datos por cuenta a nivel base de datos. Listo para producción."],
              ["LATAM-ready", "Integración nativa con Mercado Pago y focus en latencia/regulación regional."],
              ["DX impecable", "OpenAPI/SDKs, ejemplos copy-paste y Quickstart para reducir time-to-value."]
            ].map(([title, desc]) => (
              <div key={title} className="bg-white shadow-sm rounded-lg p-5 hover:shadow-md transition">
                <h3 className="font-semibold mb-2">{title}</h3>
                <p className="text-sm text-gray-600">{desc}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* ===== CÓMO FUNCIONA ===== */}
      <section className="py-12 bg-purple-50">
        <div className="max-w-7xl mx-auto px-6 text-center">
          <h2 className="text-2xl font-bold mb-8">Cómo funciona</h2>
          <div className="grid md:grid-cols-3 gap-6">
            {[
              ["1", "Ingesta tus datos", "Envía productos, usuarios e interacciones por API o lotes. Soporte de IDs externos."],
              ["2", "Entrena el modelo", "Entrenamiento automático (híbrido) optimizado para tus KPIs. Sin MLOps."],
              ["3", "Sirve recomendaciones", "Obtén recomendaciones personalizadas con latencia baja y explicación opcional (XAI)."]
            ].map(([num, title, desc]) => (
              <div key={num} className="bg-white rounded-lg p-6 shadow-sm hover:shadow-md transition">
                <div className="w-10 h-10 mx-auto mb-4 flex items-center justify-center rounded-full bg-purple-100 text-purple-600 font-bold">
                  {num}
                </div>
                <h3 className="font-semibold mb-2">{title}</h3>
                <p className="text-sm text-gray-600">{desc}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* ===== CÓDIGO ===== */}
      <section className="py-12">
        <div className="max-w-4xl mx-auto px-6 text-center">
          <h2 className="text-2xl font-bold mb-6">Tu primera recomendación en minutos</h2>
          <div className="flex justify-center gap-4 mb-4">
            {["python", "node", "curl"].map((lang) => (
              <button
                key={lang}
                onClick={() => setCodeExample(lang as "python" | "node" | "curl")}
                className={`px-4 py-2 rounded-md text-sm font-medium ${
                  codeExample === lang
                    ? "bg-purple-600 text-white"
                    : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                }`}
              >
                {lang}
              </button>
            ))}
          </div>
          <div className="relative bg-gray-900 text-green-300 rounded-lg p-4 font-mono text-sm overflow-auto">
            <button
              onClick={handleCopy}
              className="absolute top-3 right-3 bg-gray-700 text-white text-xs px-3 py-1 rounded-md hover:bg-gray-600 transition"
            >
              Copiar
            </button>
            <pre>
              <code>{codeSnippets[codeExample]}</code>
            </pre>
          </div>
          <div className="flex justify-center gap-4 mt-4">
            <Link
              href="/docs"
              className="px-5 py-2.5 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-100 transition"
            >
              Ver documentación completa
            </Link>
            <Link
              href="/quickstart"
              className="px-5 py-2.5 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-100 transition"
            >
              Ver quickstart
            </Link>
          </div>
        </div>
      </section>
    </main>
  );
}
