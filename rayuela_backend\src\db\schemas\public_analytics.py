"""
Esquemas públicos para analytics - Versión ofuscada para proteger IP.

SEGURIDAD: Estos esquemas ocultan métricas internas de ML y parámetros
de rendimiento que podrían revelar la ventaja competitiva de Rayuela.
"""

from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime
from src.utils.camel_case import CamelCaseModel


class PublicPerformanceSummary(CamelCaseModel):
    """
    Resumen público de rendimiento sin métricas técnicas de ML.
    
    SEGURIDAD: No expone precision, recall, NDCG, MAP u otras métricas
    que podrían revelar la calidad específica de los algoritmos.
    """
    recommendation_accuracy: str = Field(..., description="Nivel de precisión (High/Medium/Low)")
    user_engagement: str = Field(..., description="Nivel de engagement (Excellent/Good/Fair)")
    catalog_utilization: str = Field(..., description="Utilización del catálogo (Optimal/Good/Limited)")
    overall_performance: str = Field(..., description="Rendimiento general (Excellent/Good/Fair/Poor)")
    last_updated: datetime = Field(..., description="Última actualización de métricas")

    class Config:
        json_schema_extra = {
            "example": {
                "recommendation_accuracy": "High",
                "user_engagement": "Excellent", 
                "catalog_utilization": "Optimal",
                "overall_performance": "Excellent",
                "last_updated": "2024-01-15T10:30:00Z"
            }
        }


class PublicUsageMetrics(CamelCaseModel):
    """
    Métricas de uso públicas sin detalles de implementación.
    
    SEGURIDAD: Se enfoca en métricas de negocio, no técnicas.
    """
    total_recommendations_served: int = Field(0, description="Total de recomendaciones servidas")
    active_users: int = Field(0, description="Usuarios activos")
    api_calls_today: int = Field(0, description="Llamadas API hoy")
    average_response_time_ms: float = Field(0.0, description="Tiempo promedio de respuesta en ms")
    success_rate_percentage: float = Field(0.0, description="Tasa de éxito en porcentaje")

    class Config:
        json_schema_extra = {
            "example": {
                "total_recommendations_served": 150000,
                "active_users": 1250,
                "api_calls_today": 5430,
                "average_response_time_ms": 85.5,
                "success_rate_percentage": 99.8
            }
        }


class PublicModelStatus(CamelCaseModel):
    """
    Estado público del modelo sin detalles técnicos.
    
    SEGURIDAD: No expone versiones específicas, parámetros o arquitectura.
    """
    model_variant: str = Field(..., description="Variante del modelo (standard/premium/enterprise)")
    status: str = Field(..., description="Estado del modelo (active/training/updating)")
    last_training: Optional[datetime] = Field(None, description="Fecha del último entrenamiento")
    next_training: Optional[datetime] = Field(None, description="Fecha del próximo entrenamiento programado")
    data_freshness: str = Field(..., description="Frescura de los datos (Fresh/Moderate/Stale)")

    class Config:
        json_schema_extra = {
            "example": {
                "model_variant": "premium",
                "status": "active",
                "last_training": "2024-01-10T02:00:00Z",
                "next_training": "2024-01-17T02:00:00Z",
                "data_freshness": "Fresh"
            }
        }


class PublicRecommendationInsights(CamelCaseModel):
    """
    Insights públicos de recomendaciones sin revelar algoritmos.
    
    SEGURIDAD: Proporciona valor al cliente sin exponer IP.
    """
    top_performing_categories: List[str] = Field(default_factory=list, description="Categorías con mejor rendimiento")
    trending_items_count: int = Field(0, description="Número de items en tendencia")
    personalization_effectiveness: str = Field(..., description="Efectividad de personalización (High/Medium/Low)")
    diversity_score: str = Field(..., description="Puntuación de diversidad (Excellent/Good/Fair)")

    class Config:
        json_schema_extra = {
            "example": {
                "top_performing_categories": ["electronics", "books", "clothing"],
                "trending_items_count": 45,
                "personalization_effectiveness": "High",
                "diversity_score": "Excellent"
            }
        }


class PublicAnalyticsResponse(CamelCaseModel):
    """
    Respuesta completa de analytics públicos.
    
    SEGURIDAD: Versión pública que proporciona insights valiosos
    sin comprometer la propiedad intelectual.
    """
    performance_summary: PublicPerformanceSummary = Field(..., description="Resumen de rendimiento")
    usage_metrics: PublicUsageMetrics = Field(..., description="Métricas de uso")
    model_status: PublicModelStatus = Field(..., description="Estado del modelo")
    recommendation_insights: PublicRecommendationInsights = Field(..., description="Insights de recomendaciones")
    account_id: int = Field(..., description="ID de la cuenta")
    generated_at: datetime = Field(..., description="Fecha de generación del reporte")

    class Config:
        json_schema_extra = {
            "example": {
                "performance_summary": {
                    "recommendation_accuracy": "High",
                    "user_engagement": "Excellent",
                    "catalog_utilization": "Optimal", 
                    "overall_performance": "Excellent",
                    "last_updated": "2024-01-15T10:30:00Z"
                },
                "usage_metrics": {
                    "total_recommendations_served": 150000,
                    "active_users": 1250,
                    "api_calls_today": 5430,
                    "average_response_time_ms": 85.5,
                    "success_rate_percentage": 99.8
                },
                "model_status": {
                    "model_variant": "premium",
                    "status": "active",
                    "last_training": "2024-01-10T02:00:00Z",
                    "next_training": "2024-01-17T02:00:00Z",
                    "data_freshness": "Fresh"
                },
                "recommendation_insights": {
                    "top_performing_categories": ["electronics", "books", "clothing"],
                    "trending_items_count": 45,
                    "personalization_effectiveness": "High",
                    "diversity_score": "Excellent"
                },
                "account_id": 123,
                "generated_at": "2024-01-15T10:30:00Z"
            }
        }


def convert_internal_to_public_analytics(internal_data: Dict[str, Any]) -> PublicAnalyticsResponse:
    """
    Convierte datos internos de analytics a formato público.
    
    SEGURIDAD: Esta función abstrae métricas técnicas en categorías
    de negocio comprensibles sin revelar detalles de implementación.
    """
    
    # Convertir métricas técnicas a categorías de negocio
    def categorize_performance(precision: float, recall: float, ndcg: float) -> str:
        avg_score = (precision + recall + ndcg) / 3
        if avg_score >= 0.8:
            return "Excellent"
        elif avg_score >= 0.6:
            return "Good"
        elif avg_score >= 0.4:
            return "Fair"
        else:
            return "Poor"
    
    def categorize_accuracy(precision: float) -> str:
        if precision >= 0.8:
            return "High"
        elif precision >= 0.6:
            return "Medium"
        else:
            return "Low"
    
    def categorize_engagement(ctr: float, cvr: float) -> str:
        avg_engagement = (ctr + cvr) / 2
        if avg_engagement >= 0.1:
            return "Excellent"
        elif avg_engagement >= 0.05:
            return "Good"
        else:
            return "Fair"
    
    def categorize_coverage(coverage: float) -> str:
        if coverage >= 0.8:
            return "Optimal"
        elif coverage >= 0.6:
            return "Good"
        else:
            return "Limited"
    
    def categorize_diversity(diversity: float) -> str:
        if diversity >= 0.8:
            return "Excellent"
        elif diversity >= 0.6:
            return "Good"
        else:
            return "Fair"
    
    def categorize_freshness(hours_since_training: int) -> str:
        if hours_since_training <= 24:
            return "Fresh"
        elif hours_since_training <= 168:  # 1 week
            return "Moderate"
        else:
            return "Stale"
    
    # Extraer métricas internas (con valores por defecto seguros)
    offline_metrics = internal_data.get("offline_metrics", {})
    online_metrics = internal_data.get("online_metrics", {})
    
    precision = offline_metrics.get("precision", 0.0)
    recall = offline_metrics.get("recall", 0.0)
    ndcg = offline_metrics.get("ndcg", 0.0)
    coverage = offline_metrics.get("catalog_coverage", 0.0)
    diversity = offline_metrics.get("diversity", 0.0)
    
    ctr = online_metrics.get("ctr", 0.0)
    cvr = online_metrics.get("cvr", 0.0)
    
    # Construir respuesta pública
    return PublicAnalyticsResponse(
        performance_summary=PublicPerformanceSummary(
            recommendation_accuracy=categorize_accuracy(precision),
            user_engagement=categorize_engagement(ctr, cvr),
            catalog_utilization=categorize_coverage(coverage),
            overall_performance=categorize_performance(precision, recall, ndcg),
            last_updated=datetime.now()
        ),
        usage_metrics=PublicUsageMetrics(
            total_recommendations_served=internal_data.get("total_recommendations", 0),
            active_users=internal_data.get("active_users", 0),
            api_calls_today=internal_data.get("api_calls_today", 0),
            average_response_time_ms=internal_data.get("avg_response_time", 0.0),
            success_rate_percentage=internal_data.get("success_rate", 0.0) * 100
        ),
        model_status=PublicModelStatus(
            model_variant=internal_data.get("model_variant", "standard"),
            status=internal_data.get("model_status", "active"),
            last_training=internal_data.get("last_training"),
            next_training=internal_data.get("next_training"),
            data_freshness=categorize_freshness(internal_data.get("hours_since_training", 0))
        ),
        recommendation_insights=PublicRecommendationInsights(
            top_performing_categories=internal_data.get("top_categories", []),
            trending_items_count=internal_data.get("trending_count", 0),
            personalization_effectiveness=categorize_accuracy(precision),
            diversity_score=categorize_diversity(diversity)
        ),
        account_id=internal_data.get("account_id", 0),
        generated_at=datetime.now()
    )
