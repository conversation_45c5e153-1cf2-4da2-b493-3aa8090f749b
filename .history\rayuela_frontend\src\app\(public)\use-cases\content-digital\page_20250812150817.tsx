import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import Link from "next/link";
import { generateMetadata as generateSEOMetadata } from '@/lib/seo';
import { ArrowLeft, Play, Clock, CheckCircle, ArrowRight, Eye, Heart } from "lucide-react";

export const metadata = generateSEOMetadata({
  title: 'Contenido Digital - Casos de Uso | Rayuela',
  description: 'Retén usuarios con feeds inteligentes y recomendaciones personalizadas. Aumenta tiempo de sesión 22% y engagement en plataformas de contenido.',
  path: '/use-cases/content-digital',
  keywords: ['contenido digital', 'streaming', 'feed', 'personalización', 'engagement', 'retención'],
});

export default function ContentDigitalUseCasePage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-white via-sky-50 to-sky-100 dark:from-gray-950 dark:to-gray-900">
      <div className="container mx-auto px-4 py-16">
        <div className="mb-8">
          <Button variant="ghost" asChild className="mb-4">
            <Link href="/#use-cases">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Volver a casos de uso
            </Link>
          </Button>
          
          <div className="flex items-center gap-3 mb-4">
            <Play className="w-8 h-8 text-primary" />
            <Badge variant="outline" className="text-sm">Contenido Digital</Badge>
          </div>
          
          <h1 className="text-4xl md:text-5xl font-bold text-foreground mb-4">
            Retén usuarios con feeds inteligentes
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl mb-8">
            Transforma tu plataforma de contenido en una experiencia adictiva que mantiene
            a los usuarios enganchados con recomendaciones ultra-personalizadas.
          </p>

          {/* Historia de Éxito */}
          <div className="bg-gradient-to-r from-primary/5 to-accent/5 border border-primary/20 rounded-xl p-8 mb-8">
            <div className="flex items-start gap-4">
              <div className="w-16 h-16 bg-gradient-to-r from-primary to-accent rounded-xl flex items-center justify-center flex-shrink-0">
                <span className="text-white text-2xl">🏆</span>
              </div>
              <div>
                <h3 className="text-xl font-semibold text-foreground mb-3">
                  Caso de Éxito: NewsApp aumentó tiempo de sesión 35% y engagement 50%
                </h3>
                <p className="text-muted-foreground mb-4 leading-relaxed">
                  <strong>NewsApp</strong>, una plataforma de noticias con 500,000 usuarios activos mensuales y 2M artículos,
                  implementó Rayuela para personalizar feeds y recomendaciones. En solo 60 días:
                </p>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="bg-card border border-border rounded-lg p-4 text-center">
                    <div className="text-2xl font-bold text-success mb-1">+35%</div>
                    <div className="text-sm text-muted-foreground">Tiempo de sesión</div>
                  </div>
                  <div className="bg-card border border-border rounded-lg p-4 text-center">
                    <div className="text-2xl font-bold text-primary mb-1">+50%</div>
                    <div className="text-sm text-muted-foreground">Engagement rate</div>
                  </div>
                  <div className="bg-card border border-border rounded-lg p-4 text-center">
                    <div className="text-2xl font-bold text-warning mb-1">+28%</div>
                    <div className="text-sm text-muted-foreground">Daily active users</div>
                  </div>
                </div>
                <blockquote className="mt-4 pl-4 border-l-2 border-primary/30 italic text-muted-foreground">
                  "Rayuela transformó nuestra plataforma. Los usuarios ahora descubren contenido que realmente les interesa.
                  Nuestro engagement se disparó y la retención mejoró dramáticamente."
                  <footer className="mt-2 text-sm">
                    — <strong>Laura Mendoza</strong>, Head of Product en NewsApp
                  </footer>
                </blockquote>
              </div>
            </div>
          </div>
        </div>

        <div className="grid gap-6 md:grid-cols-3 mb-12">
          <Card className="text-center">
            <CardContent className="pt-6">
              <Clock className="w-12 h-12 text-success mx-auto mb-4" />
              <div className="text-3xl font-bold text-success mb-2">+22%</div>
              <p className="text-muted-foreground">Aumento en tiempo de sesión</p>
            </CardContent>
          </Card>
          <Card className="text-center">
            <CardContent className="pt-6">
              <Eye className="w-12 h-12 text-primary mx-auto mb-4" />
              <div className="text-3xl font-bold text-primary mb-2">+45%</div>
              <p className="text-muted-foreground">Mejora en content discovery</p>
            </CardContent>
          </Card>
          <Card className="text-center">
            <CardContent className="pt-6">
              <Heart className="w-12 h-12 text-warning mx-auto mb-4" />
              <div className="text-3xl font-bold text-warning mb-2">+31%</div>
              <p className="text-muted-foreground">Incremento en engagement rate</p>
            </CardContent>
          </Card>
        </div>

        <Card className="bg-gradient-to-r from-primary/10 to-primary/5 border-primary/20">
          <CardContent className="pt-8">
            <div className="text-center">
              <h3 className="text-2xl font-bold mb-4">
                ¿Listo para crear una experiencia adictiva?
              </h3>
              <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
                Únete a plataformas de contenido líderes que ya usan Rayuela para mantener 
                a sus usuarios enganchados más tiempo y descubriendo más contenido relevante.
              </p>
              <div className="flex gap-4 justify-center flex-wrap">
                <Button asChild size="lg">
                  <Link href="/register">
                    Empezar gratis
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Link>
                </Button>
                <Button variant="outline" asChild size="lg">
                  <Link href="/contact-sales">
                    Ver demo
                  </Link>
                </Button>
              </div>
              <p className="text-xs text-muted-foreground mt-4">
                Sin compromiso • Integración en 48 horas • Métricas en tiempo real
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
