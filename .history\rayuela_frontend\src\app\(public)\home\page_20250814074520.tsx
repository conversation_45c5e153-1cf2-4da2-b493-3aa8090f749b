"use client";

import { useCallback, useMemo, useState } from "react";
import Link from "next/link";
import Image from "next/image";
import { motion } from "framer-motion";

/**
 * Landing final:
 * - Hero con copy para CTO/PO + CTA (Quickstart/Pricing) e imagen /dashboard-hero.png
 * - Diferenciadores (XAI, LTR, Cold-start, RLS, LATAM-ready) con gradiente
 * - Cómo funciona (3 pasos) con círculos numerados centrados
 * - Snippets dinámicos (Python | Node | cURL) + botón Copiar
 * - Resultados que puedes esperar (KPIs claros, sin duplicación con el hero)
 * - Casos de éxito (3 cards con resultado + descripción breve)
 * - FAQ (objeciones típicas)
 * - CTA final
 */

type Lang = "python" | "node" | "curl";

export default function HomePage() {
  const [language, setLanguage] = useState<Lang>("python");
  const [copied, setCopied] = useState(false);

  const snippets: Record<Lang, string> = useMemo(
    () => ({
      python: `# pip install rayuela-sdk
from rayuela import Client

client = Client(api_key="YOUR_API_KEY")
recos = client.get_recommendations(user_id="123", limit=10)
for r in recos:
    print(r["item_id"], r["score"])`,
      node: `// npm i rayuela-sdk
import Rayuela from "rayuela-sdk";

const client = new Rayuela({ apiKey: "YOUR_API_KEY" });
const recos = await client.getRecommendations({ userId: "123", limit: 10 });
console.log(recos);`,
      curl: `curl -X POST "https://api.rayuela.ai/v1/recommendations/personalized/query" \\
  -H "Authorization: Bearer YOUR_API_KEY" \\
  -H "Content-Type: application/json" \\
  -d '{"user_id":"123","limit":10}'`,
    }),
    []
  );

  const onCopy = useCallback(async () => {
    try {
      await navigator.clipboard.writeText(snippets[language]);
      setCopied(true);
      setTimeout(() => setCopied(false), 1600);
    } catch {
      // noop
    }
  }, [language, snippets]);

  // Animations
  const fadeUp = {
    hidden: { opacity: 0, y: 24 },
    show: { opacity: 1, y: 0, transition: { duration: 0.55, ease: "easeOut" } },
  };

  const springCard = {
    hidden: { opacity: 0, y: 16 },
    show: (i: number) => ({
      opacity: 1,
      y: 0,
      transition: { duration: 0.45, delay: 0.08 * i },
    }),
  };

  return (
    <main className="bg-white">
      {/* ===== HERO ===== */}
      <section className="py-16 md:py-20">
        <div className="max-w-7xl mx-auto px-6 grid md:grid-cols-2 gap-10 items-center">
          <motion.div
            variants={fadeUp}
            initial="hidden"
            animate="show"
            className="order-2 md:order-1"
          >
            <h1 className="text-4xl md:text-5xl font-bold leading-tight mb-4">
              Personalización con IA para E-commerce y Marketplaces
            </h1>
            <p className="text-lg text-gray-600 mb-6">
              Rayuela es una API-first de recomendaciones: integración en <strong>horas</strong>, impacto en
              <strong> conversión y AOV</strong>, sin MLOps ni complejidad. Diseñada para CTOs, Product Leaders y devs.
            </p>
            <div className="flex flex-wrap gap-4">
              <Link href="/register" className="btn-primary" aria-label="Crear cuenta gratis">
                Comenzar ahora
              </Link>
              <Link href="/pricing" className="btn-secondary" aria-label="Ver planes y precios">
                Ver planes
              </Link>
            </div>
            <div className="mt-6 text-sm text-gray-500">
              <span className="mr-2">✅ Free sandbox</span>
              <span className="mr-2">✅ API Keys instantáneas</span>
              <span>✅ SDKs para Python y Node</span>
            </div>
          </motion.div>

          <motion.div
            className="order-1 md:order-2 flex justify-center"
            initial={{ opacity: 0, y: 32 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <Image
              src="/dashboard-hero.png"
              alt="Rayuela dashboard: métricas y recomendaciones"
              width={680}
              height={520}
              priority
              className="hero-img"
            />
          </motion.div>
        </div>
      </section>

      {/* ===== DIFERENCIADORES ===== */}
      <section className="section-gradient-strong">
        <div className="max-w-7xl mx-auto px-6">
          <motion.h2
            variants={fadeUp}
            initial="hidden"
            whileInView="show"
            viewport={{ once: true, amount: 0.3 }}
            className="text-3xl font-bold text-center mb-10"
          >
            Diferenciadores clave
          </motion.h2>

          <div className="grid md:grid-cols-3 gap-6">
            {[
              {
                t: "IA explicable (XAI)",
                d: "Entiende por qué se recomienda cada ítem. Transparencia para PMs, confianza para tu negocio.",
              },
              {
                t: "Optimizado con LTR",
                d: "Learning-to-Rank entrenado para maximizar métricas de negocio (conversión, AOV), no solo precisión.",
              },
              {
                t: "Cold-start sin fricción",
                d: "Fallback inteligente y señales iniciales para mostrar recomendaciones relevantes desde el día uno.",
              },
              {
                t: "Multi-tenant seguro (RLS)",
                d: "Aislamiento de datos por cuenta a nivel base de datos. Listo para producción.",
              },
              {
                t: "LATAM-ready",
                d: "Integración nativa con Mercado Pago y focus en latencia/regulación regional.",
              },
              {
                t: "DX impecable",
                d: "OpenAPI/SDKs, ejemplos copy-paste y Quickstart para reducir time-to-value.",
              },
            ].map((f, i) => (
              <motion.div
                key={f.t}
                className="card"
                variants={springCard}
                initial="hidden"
                whileInView="show"
                viewport={{ once: true, amount: 0.2 }}
                custom={i}
                whileHover={{ scale: 1.02 }}
                transition={{ type: "spring", stiffness: 120, damping: 16 }}
              >
                <h3 className="text-lg font-semibold mb-2">{f.t}</h3>
                <p className="text-gray-600">{f.d}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* ===== CÓMO FUNCIONA ===== */}
      <section className="section-gradient-strong">
        <div className="max-w-7xl mx-auto px-6">
          <motion.h2
            variants={fadeUp}
            initial="hidden"
            whileInView="show"
            viewport={{ once: true, amount: 0.3 }}
            className="text-3xl font-bold text-center mb-10"
          >
            Cómo funciona
          </motion.h2>

          <div className="grid md:grid-cols-3 gap-6">
            {[
              {
                n: "1",
                t: "Ingesta tus datos",
                d: "Envía productos, usuarios e interacciones por API o lotes. Soporte de IDs externos.",
              },
              {
                n: "2",
                t: "Entrena el modelo",
                d: "Entrenamiento automático (híbrido) optimizado para tus KPIs. Sin MLOps.",
              },
              {
                n: "3",
                t: "Sirve recomendaciones",
                d: "Obtén recomendaciones personalizadas con latencia baja y explicación opcional (XAI).",
              },
            ].map((s, i) => (
              <motion.div
                key={s.t}
                className="card text-center"
                variants={springCard}
                initial="hidden"
                whileInView="show"
                viewport={{ once: true, amount: 0.2 }}
                custom={i}
                whileHover={{ scale: 1.02 }}
              >
                <div className="step-circle">{s.n}</div>
                <h3 className="text-lg font-semibold mb-2">{s.t}</h3>
                <p className="text-gray-600">{s.d}</p>
              </motion.div>
            ))}
          </div>

          <div className="flex justify-center mt-8">
            <Link href="/docs/quickstart" className="btn-primary" aria-label="Ir al Quickstart">
              Integrar en 3 pasos
            </Link>
          </div>
        </div>
      </section>

      {/* ===== CODE SNIPPETS ===== */}
      <section className="py-16 bg-white">
        <div className="max-w-4xl mx-auto px-6">
          <motion.h2
            variants={fadeUp}
            initial="hidden"
            whileInView="show"
            viewport={{ once: true, amount: 0.3 }}
            className="text-3xl font-bold text-center mb-6"
          >
            Tu primera recomendación en minutos
          </motion.h2>

          <motion.div
            variants={fadeUp}
            initial="hidden"
            whileInView="show"
            viewport={{ once: true, amount: 0.2 }}
            className="flex flex-wrap items-center justify-center gap-3 mb-5"
          >
            {(["python", "node", "curl"] as Lang[]).map((lang) => (
              <button
                key={lang}
                onClick={() => setLanguage(lang)}
                className={`px-4 py-2 rounded transition ${
                  language === lang ? "bg-purple-600 text-white" : "bg-gray-200 text-gray-800"
                }`}
                aria-pressed={language === lang}
                aria-label={`Mostrar snippet ${lang}`}
              >
                {lang}
              </button>
            ))}
            <button
              onClick={onCopy}
              className="px-4 py-2 rounded border border-gray-300 text-gray-700 hover:bg-gray-50"
              aria-label="Copiar snippet al portapapeles"
            >
              {copied ? "Copiado ✓" : "Copiar"}
            </button>
          </motion.div>

          <motion.pre
            key={language}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.25 }}
            className="bg-gray-900 text-green-400 rounded-lg p-6 text-left overflow-x-auto"
          >
{snippets[language]}
          </motion.pre>

          <div className="text-center mt-6">
            <Link href="/docs" className="btn-secondary" aria-label="Ir a la documentación completa">
              Ver documentación completa
            </Link>
          </div>
        </div>
      </section>

      {/* ===== RESULTADOS QUE PUEDES ESPERAR ===== */}
      <section className="section-gradient-strong">
        <div className="max-w-7xl mx-auto px-6">
          <motion.h2
            variants={fadeUp}
            initial="hidden"
            whileInView="show"
            viewport={{ once: true, amount: 0.3 }}
            className="text-3xl font-bold text-center mb-10"
          >
            Resultados que puedes esperar
          </motion.h2>

          <div className="grid md:grid-cols-3 gap-6">
            {[
              { k: "+15%", t: "Conversión", d: "Incremento promedio tras 30-45 días." },
              { k: "+12%", t: "AOV", d: "Ticket promedio impulsado por recomendaciones." },
              { k: "-25%", t: "Tiempo de integración", d: "vs. construir un RecSys in-house." },
            ].map((m, i) => (
              <motion.div
                key={m.t}
                className="card text-center"
                variants={springCard}
                initial="hidden"
                whileInView="show"
                viewport={{ once: true, amount: 0.2 }}
                custom={i}
              >
                <div className="text-3xl font-extrabold mb-2">{m.k}</div>
                <div className="font-semibold mb-1">{m.t}</div>
                <p className="text-gray-600">{m.d}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* ===== CASOS DE ÉXITO ===== */}
      <section className="section-gradient-strong">
        <div className="max-w-7xl mx-auto px-6">
          <motion.h2
            variants={fadeUp}
            initial="hidden"
            whileInView="show"
            viewport={{ once: true, amount: 0.3 }}
            className="text-3xl font-bold text-center mb-10"
          >
            Casos de éxito
          </motion.h2>

          <div className="grid md:grid-cols-3 gap-6">
            {[
              {
                name: "MercadoX",
                result: "+18% conversión",
                desc:
                  "Marketplace regional integró Rayuela en 6 días. Recomendaciones explainables generaron confianza en PMs y devs.",
              },
              {
                name: "NovaShop",
                result: "+10% AOV",
                desc:
                  "E-commerce mid-market potenció cross-sell con LTR. Latencia estable y DX rápida para su equipo.",
              },
              {
                name: "TiendaBase",
                result: "-30% time-to-market",
                desc:
                  "Startup reemplazó mes de desarrollo in-house por Rayuela. Onboarding guiado y sandbox free.",
              },
            ].map((c, i) => (
              <motion.div
                key={c.name}
                className="card"
                variants={springCard}
                initial="hidden"
                whileInView="show"
                viewport={{ once: true, amount: 0.2 }}
                custom={i}
                whileHover={{ scale: 1.02 }}
              >
                <div className="text-sm text-gray-500 mb-1">{c.name}</div>
                <div className="text-xl font-bold mb-2">{c.result}</div>
                <p className="text-gray-600">{c.desc}</p>
              </motion.div>
            ))}
          </div>

          <div className="flex justify-center mt-8">
            <Link href="/register?utm_campaign=success-stories" className="btn-primary" aria-label="Crear cuenta">
              Únete como early adopter
            </Link>
          </div>
        </div>
      </section>

      {/* ===== FAQ ===== */}
      <section className="py-16 bg-white">
        <div className="max-w-5xl mx-auto px-6">
          <motion.h2
            variants={fadeUp}
            initial="hidden"
            whileInView="show"
            viewport={{ once: true, amount: 0.3 }}
            className="text-3xl font-bold text-center mb-10"
          >
            Preguntas frecuentes
          </motion.h2>

          <div className="grid md:grid-cols-2 gap-6">
            {[
              {
                q: "¿Necesito un equipo de ML para usar Rayuela?",
                a: "No. Rayuela abstrae el MLOps y ofrece defaults sólidos (híbrido + LTR). Si tienes Data Scientists/ML Engineers, pueden ajustar estrategias y explicar salidas con XAI.",
              },
              {
                q: "¿Cómo se maneja el cold-start?",
                a: "Usamos fallback inteligente (best-sellers, similitudes y señales iniciales). Aseguramos relevancia desde el primer día.",
              },
              {
                q: "¿Qué tan rápido puedo integrar?",
                a: "Con el Quickstart, puedes servir recomendaciones en horas. Nuestra DX (SDKs, OpenAPI y ejemplos) reduce drásticamente el time-to-value.",
              },
              {
                q: "¿Es seguro para datos sensibles?",
                a: "Sí. Arquitectura multi-tenant con RLS y aislamiento por cuenta. Logs y auditoría a nivel de API.",
              },
            ].map((f, i) => (
              <motion.div
                key={f.q}
                className="card"
                variants={springCard}
                initial="hidden"
                whileInView="show"
                viewport={{ once: true, amount: 0.2 }}
                custom={i}
              >
                <h3 className="font-semibold mb-2">{f.q}</h3>
                <p className="text-gray-600">{f.a}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* ===== CTA FINAL ===== */}
      <section className="section-gradient-strong">
        <div className="max-w-4xl mx-auto px-6 text-center">
          <motion.h2
            variants={fadeUp}
            initial="hidden"
            whileInView="show"
            viewport={{ once: true, amount: 0.3 }}
            className="text-3xl font-bold mb-4"
          >
            Empieza gratis y llega a tu primera recomendación hoy
          </motion.h2>
          <p className="text-gray-600 mb-6">
            Crea tu cuenta, genera tu API Key y sigue el Quickstart. Si necesitas ayuda, te acompañamos en la integración.
          </p>
          <div className="flex justify-center gap-4">
            <Link href="/register" className="btn-primary" aria-label="Crear cuenta">
              Crear cuenta
            </Link>
            <Link href="/docs/quickstart" className="btn-secondary" aria-label="Ir al Quickstart">
              Ver Quickstart
            </Link>
          </div>
        </div>
      </section>
    </main>
  );
}
