import React from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import Link from "next/link";
import { generateMetadata as generateSEOMetadata } from '@/lib/seo';
import { ArrowLeft, Search, Clock, Users, Store, CheckCircle, ArrowRight, Zap } from "lucide-react";

export const metadata = generateSEOMetadata({
  title: 'Marketplaces - Casos de Uso | Rayuela',
  description: 'Optimiza la conexión entre oferta y demanda en tu marketplace. Reduce tiempo de búsqueda 25% y aumenta la satisfacción de compradores y vendedores.',
  path: '/use-cases/marketplaces',
  keywords: ['marketplace', 'búsqueda', 'matching', 'oferta', 'demanda', 'discovery'],
});

export default function MarketplacesUseCasePage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-white via-sky-50 to-sky-100 dark:from-gray-950 dark:to-gray-900">
      <div className="container mx-auto px-4 py-16">
        {/* Header */}
        <div className="mb-8">
          <Button variant="ghost" asChild className="mb-4">
            <Link href="/#use-cases">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Volver a casos de uso
            </Link>
          </Button>
          
          <div className="flex items-center gap-3 mb-4">
            <Store className="w-8 h-8 text-primary" />
            <Badge variant="outline" className="text-sm">Marketplaces</Badge>
          </div>
          
          <h1 className="text-4xl md:text-5xl font-bold text-foreground mb-4">
            Conecta oferta y demanda de forma inteligente
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl mb-8">
            Transforma tu marketplace en una plataforma que encuentra automáticamente lo que cada usuario busca,
            reduciendo fricción y aumentando transacciones.
          </p>

          {/* Historia de Éxito */}
          <div className="bg-gradient-to-r from-primary/5 to-accent/5 border border-primary/20 rounded-xl p-8 mb-8">
            <div className="flex items-start gap-4">
              <div className="w-16 h-16 bg-gradient-to-r from-primary to-accent rounded-xl flex items-center justify-center flex-shrink-0">
                <span className="text-white text-2xl">🏆</span>
              </div>
              <div>
                <h3 className="text-xl font-semibold text-foreground mb-3">
                  Caso de Éxito: ServiceHub redujo tiempo de búsqueda 40% y aumentó GMV 25%
                </h3>
                <p className="text-muted-foreground mb-4 leading-relaxed">
                  <strong>ServiceHub</strong>, un marketplace de servicios profesionales con 10,000 proveedores y 50,000 búsquedas mensuales,
                  implementó Rayuela para mejorar el matching entre clientes y proveedores. En solo 45 días:
                </p>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="bg-card border border-border rounded-lg p-4 text-center">
                    <div className="text-2xl font-bold text-success mb-1">-40%</div>
                    <div className="text-sm text-muted-foreground">Tiempo de búsqueda</div>
                  </div>
                  <div className="bg-card border border-border rounded-lg p-4 text-center">
                    <div className="text-2xl font-bold text-primary mb-1">+25%</div>
                    <div className="text-sm text-muted-foreground">GMV total</div>
                  </div>
                  <div className="bg-card border border-border rounded-lg p-4 text-center">
                    <div className="text-2xl font-bold text-warning mb-1">+60%</div>
                    <div className="text-sm text-muted-foreground">Matches exitosos</div>
                  </div>
                </div>
                <blockquote className="mt-4 pl-4 border-l-2 border-primary/30 italic text-muted-foreground">
                  "Rayuela transformó nuestro marketplace. Los usuarios encuentran exactamente lo que buscan sin frustrarse.
                  Nuestros proveedores están más felices porque reciben leads más calificados."
                  <footer className="mt-2 text-sm">
                    — <strong>Roberto Silva</strong>, CTO de ServiceHub
                  </footer>
                </blockquote>
              </div>
            </div>
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid gap-6 md:grid-cols-3 mb-12">
          <Card className="text-center">
            <CardContent className="pt-6">
              <Clock className="w-12 h-12 text-success mx-auto mb-4" />
              <div className="text-3xl font-bold text-success mb-2">-25%</div>
              <p className="text-muted-foreground">Reducción en tiempo de búsqueda</p>
            </CardContent>
          </Card>
          <Card className="text-center">
            <CardContent className="pt-6">
              <Search className="w-12 h-12 text-primary mx-auto mb-4" />
              <div className="text-3xl font-bold text-primary mb-2">+40%</div>
              <p className="text-muted-foreground">Mejora en discovery de productos</p>
            </CardContent>
          </Card>
          <Card className="text-center">
            <CardContent className="pt-6">
              <Users className="w-12 h-12 text-warning mx-auto mb-4" />
              <div className="text-3xl font-bold text-warning mb-2">+18%</div>
              <p className="text-muted-foreground">Aumento en transacciones completadas</p>
            </CardContent>
          </Card>
        </div>

        {/* Diagrama de Implementación */}
        <Card className="mb-12">
          <CardHeader>
            <CardTitle>Dónde implementar matching inteligente para máximo impacto</CardTitle>
            <CardDescription>
              Puntos estratégicos que mejoran la experiencia de compradores y vendedores
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Diagrama Visual */}
              <div className="space-y-6">
                <h4 className="font-semibold text-lg mb-4">Flujo del Usuario</h4>

                {[
                  {
                    step: '1',
                    title: 'Búsqueda Inicial',
                    description: '"Proveedores recomendados para tu necesidad"',
                    impact: '+45% precisión en resultados',
                    color: 'from-blue-500 to-cyan-500'
                  },
                  {
                    step: '2',
                    title: 'Filtros Inteligentes',
                    description: '"Basándote en tu ubicación y presupuesto"',
                    impact: '-40% tiempo de búsqueda',
                    color: 'from-green-500 to-emerald-500'
                  },
                  {
                    step: '3',
                    title: 'Matching Automático',
                    description: '"Estos proveedores son perfectos para ti"',
                    impact: '+60% matches exitosos',
                    color: 'from-purple-500 to-violet-500'
                  },
                  {
                    step: '4',
                    title: 'Recomendaciones Post-Compra',
                    description: '"Servicios complementarios que podrías necesitar"',
                    impact: '+30% servicios adicionales',
                    color: 'from-orange-500 to-red-500'
                  }
                ].map((item) => (
                  <div key={item.step} className="flex items-start gap-4">
                    <div className={`w-10 h-10 bg-gradient-to-r ${item.color} rounded-full flex items-center justify-center text-white font-bold flex-shrink-0`}>
                      {item.step}
                    </div>
                    <div className="flex-1">
                      <h5 className="font-semibold text-foreground mb-1">{item.title}</h5>
                      <p className="text-sm text-muted-foreground mb-2">{item.description}</p>
                      <div className="bg-success/10 border border-success/20 rounded-lg px-3 py-1 inline-block">
                        <span className="text-xs font-medium text-success">{item.impact}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Mockup Visual */}
              <div className="bg-slate-50 dark:bg-slate-800 rounded-xl p-6">
                <h4 className="font-semibold text-lg mb-4">Ejemplo Visual</h4>
                <div className="space-y-4">
                  <div className="bg-white dark:bg-slate-700 rounded-lg p-4 border border-slate-200 dark:border-slate-600">
                    <div className="text-sm font-medium text-foreground mb-2">🔍 Búsqueda</div>
                    <div className="text-xs text-muted-foreground">"Plomero cerca de ti"</div>
                    <div className="flex gap-2 mt-2">
                      {['🔧', '⭐', '📍'].map((item) => (
                        <div key={item} className="w-8 h-8 bg-primary/10 rounded flex items-center justify-center text-xs">
                          {item}
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="bg-white dark:bg-slate-700 rounded-lg p-4 border border-slate-200 dark:border-slate-600">
                    <div className="text-sm font-medium text-foreground mb-2">🎯 Matching</div>
                    <div className="text-xs text-muted-foreground">"95% compatible contigo"</div>
                    <div className="flex gap-2 mt-2">
                      {['👨‍🔧', '💰', '⏰'].map((item) => (
                        <div key={item} className="w-8 h-8 bg-accent/10 rounded flex items-center justify-center text-xs">
                          {item}
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="bg-white dark:bg-slate-700 rounded-lg p-4 border border-slate-200 dark:border-slate-600">
                    <div className="text-sm font-medium text-foreground mb-2">➕ Complementarios</div>
                    <div className="text-xs text-muted-foreground">"También podrías necesitar"</div>
                    <div className="flex gap-2 mt-2">
                      {['🔌', '🎨', '🧹'].map((item) => (
                        <div key={item} className="w-8 h-8 bg-warning/10 rounded flex items-center justify-center text-xs">
                          {item}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Problem & Solution */}
        <div className="grid gap-8 md:grid-cols-2 mb-12">
          <Card className="border-destructive/20">
            <CardHeader>
              <CardTitle className="text-destructive">Desafíos del Marketplace</CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-3 text-muted-foreground">
                <li>• <strong>Búsqueda ineficiente:</strong> Usuarios no encuentran lo que buscan</li>
                <li>• <strong>Catálogo abrumador:</strong> Demasiadas opciones confunden al comprador</li>
                <li>• <strong>Matching pobre:</strong> Productos relevantes quedan ocultos</li>
                <li>• <strong>Abandono alto:</strong> Usuarios se frustran y se van sin comprar</li>
                <li>• <strong>Vendedores invisibles:</strong> Productos de calidad no se descubren</li>
              </ul>
            </CardContent>
          </Card>

          <Card className="border-success/20">
            <CardHeader>
              <CardTitle className="text-success">Solución Inteligente</CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-3 text-muted-foreground">
                <li>• <strong>Discovery automático:</strong> Productos relevantes aparecen sin buscar</li>
                <li>• <strong>Búsqueda semántica:</strong> Entiende intención, no solo keywords</li>
                <li>• <strong>Matching inteligente:</strong> Conecta compradores con vendedores ideales</li>
                <li>• <strong>Experiencia fluida:</strong> Menos clics, más conversiones</li>
                <li>• <strong>Visibilidad justa:</strong> Todos los vendedores tienen oportunidad</li>
              </ul>
            </CardContent>
          </Card>
        </div>

        {/* Use Cases by Marketplace Type */}
        <Card className="mb-12">
          <CardHeader>
            <CardTitle>Casos por Tipo de Marketplace</CardTitle>
            <CardDescription>
              Rayuela se adapta a diferentes modelos de negocio
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-8 md:grid-cols-2">
              <div className="space-y-6">
                <div>
                  <h4 className="font-semibold text-lg mb-3">🛍️ Marketplace de Productos</h4>
                  <div className="space-y-3">
                    <div className="bg-slate-50 dark:bg-slate-800 p-4 rounded-lg">
                      <h5 className="font-medium mb-2">Recomendaciones por categoría</h5>
                      <p className="text-sm text-muted-foreground">
                        "Si buscas laptops, también te pueden interesar estos accesorios de vendedores verificados"
                      </p>
                    </div>
                    <div className="bg-slate-50 dark:bg-slate-800 p-4 rounded-lg">
                      <h5 className="font-medium mb-2">Matching precio-calidad</h5>
                      <p className="text-sm text-muted-foreground">
                        Conecta automáticamente compradores con vendedores que ofrecen el mejor valor
                      </p>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="font-semibold text-lg mb-3">🏠 Marketplace de Servicios</h4>
                  <div className="space-y-3">
                    <div className="bg-slate-50 dark:bg-slate-800 p-4 rounded-lg">
                      <h5 className="font-medium mb-2">Matching por ubicación y skills</h5>
                      <p className="text-sm text-muted-foreground">
                        Conecta clientes con profesionales cercanos que tienen las habilidades exactas
                      </p>
                    </div>
                    <div className="bg-slate-50 dark:bg-slate-800 p-4 rounded-lg">
                      <h5 className="font-medium mb-2">Recomendaciones de servicios complementarios</h5>
                      <p className="text-sm text-muted-foreground">
                        "Después de contratar plomería, podrías necesitar estos otros servicios"
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="space-y-6">
                <div>
                  <h4 className="font-semibold text-lg mb-3">🏢 B2B Marketplace</h4>
                  <div className="space-y-3">
                    <div className="bg-slate-50 dark:bg-slate-800 p-4 rounded-lg">
                      <h5 className="font-medium mb-2">Matching por industria y volumen</h5>
                      <p className="text-sm text-muted-foreground">
                        Conecta compradores corporativos con proveedores que manejan su escala
                      </p>
                    </div>
                    <div className="bg-slate-50 dark:bg-slate-800 p-4 rounded-lg">
                      <h5 className="font-medium mb-2">Recomendaciones de proveedores alternativos</h5>
                      <p className="text-sm text-muted-foreground">
                        Sugiere opciones de backup y diversificación de supply chain
                      </p>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="font-semibold text-lg mb-3">🎨 Marketplace de Contenido</h4>
                  <div className="space-y-3">
                    <div className="bg-slate-50 dark:bg-slate-800 p-4 rounded-lg">
                      <h5 className="font-medium mb-2">Discovery por estilo y tema</h5>
                      <p className="text-sm text-muted-foreground">
                        Conecta compradores con creadores que hacen el estilo exacto que buscan
                      </p>
                    </div>
                    <div className="bg-slate-50 dark:bg-slate-800 p-4 rounded-lg">
                      <h5 className="font-medium mb-2">Recomendaciones de contenido relacionado</h5>
                      <p className="text-sm text-muted-foreground">
                        "Si te gustó este diseño, estos otros creadores tienen estilos similares"
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Implementation Strategy */}
        <Card className="mb-12">
          <CardHeader>
            <CardTitle>Estrategia de Implementación</CardTitle>
            <CardDescription>
              Cómo integrar Rayuela en tu marketplace paso a paso
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-6 md:grid-cols-3">
              <div className="text-center">
                <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-primary font-bold">1</span>
                </div>
                <h4 className="font-semibold mb-2">Fase Discovery</h4>
                <p className="text-sm text-muted-foreground">
                  Implementa recomendaciones en homepage y páginas de categoría para mejorar el discovery inicial
                </p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-primary font-bold">2</span>
                </div>
                <h4 className="font-semibold mb-2">Fase Search</h4>
                <p className="text-sm text-muted-foreground">
                  Mejora resultados de búsqueda con ranking inteligente y sugerencias de productos relacionados
                </p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-primary font-bold">3</span>
                </div>
                <h4 className="font-semibold mb-2">Fase Conversion</h4>
                <p className="text-sm text-muted-foreground">
                  Optimiza páginas de producto y checkout con recomendaciones que aumentan el valor de transacción
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Benefits for Stakeholders */}
        <Card className="mb-12">
          <CardHeader>
            <CardTitle>Beneficios para Todos los Stakeholders</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-6 md:grid-cols-3">
              <div>
                <h4 className="font-semibold text-lg mb-4 flex items-center gap-2">
                  <Users className="w-5 h-5 text-primary" />
                  Para Compradores
                </h4>
                <ul className="space-y-2 text-sm text-muted-foreground">
                  <li>• Encuentran productos relevantes más rápido</li>
                  <li>• Descubren opciones que no sabían que existían</li>
                  <li>• Menos tiempo navegando, más tiempo decidiendo</li>
                  <li>• Mejor relación precio-calidad</li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold text-lg mb-4 flex items-center gap-2">
                  <Store className="w-5 h-5 text-success" />
                  Para Vendedores
                </h4>
                <ul className="space-y-2 text-sm text-muted-foreground">
                  <li>• Mayor visibilidad de sus productos</li>
                  <li>• Conexión con compradores ideales</li>
                  <li>• Aumento en tasa de conversión</li>
                  <li>• Competencia más justa basada en relevancia</li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold text-lg mb-4 flex items-center gap-2">
                  <Zap className="w-5 h-5 text-warning" />
                  Para el Marketplace
                </h4>
                <ul className="space-y-2 text-sm text-muted-foreground">
                  <li>• Aumento en GMV (Gross Merchandise Value)</li>
                  <li>• Mayor retención de usuarios</li>
                  <li>• Reducción en costos de customer support</li>
                  <li>• Diferenciación competitiva</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Technical Implementation */}
        <Card className="mb-12">
          <CardHeader>
            <CardTitle>Implementación Técnica Simplificada</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2">
              <div className="flex items-start gap-3">
                <CheckCircle className="w-5 h-5 text-success mt-1" />
                <div>
                  <h4 className="font-semibold">API de filtros avanzados</h4>
                  <p className="text-sm text-muted-foreground">
                    Filtra por ubicación, precio, rating, disponibilidad y características específicas
                  </p>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <CheckCircle className="w-5 h-5 text-success mt-1" />
                <div>
                  <h4 className="font-semibold">Ranking personalizado</h4>
                  <p className="text-sm text-muted-foreground">
                    Ordena resultados basándose en preferencias individuales del usuario
                  </p>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <CheckCircle className="w-5 h-5 text-success mt-1" />
                <div>
                  <h4 className="font-semibold">Búsqueda semántica</h4>
                  <p className="text-sm text-muted-foreground">
                    Entiende sinónimos, contexto e intención más allá de keywords exactas
                  </p>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <CheckCircle className="w-5 h-5 text-success mt-1" />
                <div>
                  <h4 className="font-semibold">Analytics de matching</h4>
                  <p className="text-sm text-muted-foreground">
                    Métricas de qué tan bien conectas oferta y demanda en tiempo real
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Explicación Técnica para CTOs */}
        <Card className="mb-12">
          <CardHeader>
            <CardTitle>Integración técnica para marketplaces</CardTitle>
            <CardDescription>
              Para CTOs evaluando el esfuerzo de implementación en plataformas multi-vendor
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Esquema de Arquitectura */}
              <div>
                <h4 className="font-semibold text-lg mb-4">Arquitectura de Matching</h4>
                <div className="bg-slate-50 dark:bg-slate-800 rounded-xl p-6">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="bg-blue-100 dark:bg-blue-900 rounded-lg p-3 text-center flex-1 mr-2">
                        <div className="text-sm font-medium">Tu Marketplace</div>
                        <div className="text-xs text-muted-foreground">Compradores + Vendedores</div>
                      </div>
                      <div className="text-primary">→</div>
                      <div className="bg-primary/10 rounded-lg p-3 text-center flex-1 ml-2">
                        <div className="text-sm font-medium">Rayuela Matching</div>
                        <div className="text-xs text-muted-foreground">ML + Filtros</div>
                      </div>
                    </div>

                    <div className="text-center text-sm text-muted-foreground">
                      <div className="mb-2">Flujo de matching:</div>
                      <div className="space-y-1 text-xs">
                        <div>1. Usuario busca servicio/producto</div>
                        <div>2. Rayuela analiza perfil + contexto</div>
                        <div>3. Ranking inteligente de proveedores</div>
                        <div>4. Matching score + explicación</div>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="mt-6 space-y-3">
                  <div className="flex items-center gap-3">
                    <CheckCircle className="w-5 h-5 text-success" />
                    <span className="text-sm">Funciona con cualquier stack</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <CheckCircle className="w-5 h-5 text-success" />
                    <span className="text-sm">API REST con filtros avanzados</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <CheckCircle className="w-5 h-5 text-success" />
                    <span className="text-sm">Escalable a millones de matches</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <CheckCircle className="w-5 h-5 text-success" />
                    <span className="text-sm">Analytics de matching en tiempo real</span>
                  </div>
                </div>
              </div>

              {/* Ejemplo JSON */}
              <div>
                <h4 className="font-semibold text-lg mb-4">Ejemplo Request/Response</h4>
                <div className="space-y-4">
                  <div>
                    <h5 className="text-sm font-medium mb-2 text-muted-foreground">REQUEST</h5>
                    <div className="bg-slate-800 rounded-lg p-4 text-sm font-mono text-slate-100 overflow-x-auto">
                      <pre>{`POST /api/v1/match
{
  "buyerId": "buyer_123",
  "query": "plomero urgente",
  "location": {
    "lat": -34.6037,
    "lng": -58.3816
  },
  "filters": {
    "maxDistance": 10,
    "availability": "today",
    "minRating": 4.0
  },
  "limit": 10
}`}</pre>
                    </div>
                  </div>

                  <div>
                    <h5 className="text-sm font-medium mb-2 text-muted-foreground">RESPONSE</h5>
                    <div className="bg-slate-800 rounded-lg p-4 text-sm font-mono text-slate-100 overflow-x-auto">
                      <pre>{`{
  "matches": [
    {
      "providerId": "prov_456",
      "matchScore": 0.95,
      "distance": 2.3,
      "reasons": [
        "alta_compatibilidad",
        "disponible_hoy",
        "especialista_plomeria"
      ]
    }
  ],
  "totalMatches": 23,
  "responseTime": "38ms"
}`}</pre>
                    </div>
                  </div>
                </div>

                <div className="mt-6 bg-success/10 border border-success/20 rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="text-success">⚡</span>
                    <span className="font-semibold text-success">Tiempo de implementación</span>
                  </div>
                  <div className="text-sm text-muted-foreground space-y-1">
                    <div>• <strong>Setup inicial:</strong> 3-5 horas</div>
                    <div>• <strong>Integración matching:</strong> 6-8 horas</div>
                    <div>• <strong>Testing y optimización:</strong> 4-6 horas</div>
                    <div className="pt-2 border-t border-success/20">
                      <strong className="text-success">Total: 2-3 días de desarrollo</strong>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Pruebas Sociales - Marketplaces */}
        <Card className="mb-12">
          <CardHeader>
            <CardTitle>Lo que dicen nuestros clientes de marketplaces</CardTitle>
            <CardDescription>
              Testimonios reales de plataformas que ya están usando Rayuela
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
              {[
                {
                  quote: "Rayuela transformó nuestro marketplace. Los usuarios encuentran exactamente lo que buscan sin frustrarse.",
                  author: "Roberto Silva",
                  role: "CTO",
                  company: "ServiceHub",
                  avatar: "👨‍💼",
                  metrics: "-40% tiempo búsqueda, +25% GMV"
                },
                {
                  quote: "Nuestros proveedores están más felices porque reciben leads más calificados. El matching es increíble.",
                  author: "Carmen López",
                  role: "Head of Operations",
                  company: "FreelanceConnect",
                  avatar: "👩‍💻",
                  metrics: "+60% matches exitosos, +35% satisfacción"
                },
                {
                  quote: "Implementamos en 3 días y vimos resultados inmediatos. La plataforma se siente mucho más inteligente.",
                  author: "Diego Martínez",
                  role: "Product Manager",
                  company: "LocalServices",
                  avatar: "👨‍🚀",
                  metrics: "+45% precisión, +30% conversión"
                }
              ].map((testimonial) => (
                <div
                  key={testimonial.author}
                  className="bg-card border border-border rounded-xl p-6 hover:shadow-lg transition-all duration-300"
                >
                  <blockquote className="text-muted-foreground mb-4 leading-relaxed italic">
                    "{testimonial.quote}"
                  </blockquote>
                  <div className="flex items-center gap-3 mb-3">
                    <div className="w-12 h-12 bg-gradient-to-r from-primary/20 to-accent/20 rounded-full flex items-center justify-center">
                      <span className="text-xl">{testimonial.avatar}</span>
                    </div>
                    <div>
                      <div className="font-semibold text-foreground">{testimonial.author}</div>
                      <div className="text-sm text-muted-foreground">{testimonial.role}</div>
                      <div className="text-xs text-muted-foreground/70">{testimonial.company}</div>
                    </div>
                  </div>
                  <div className="bg-success/10 border border-success/20 rounded-lg px-3 py-2">
                    <span className="text-xs font-medium text-success">{testimonial.metrics}</span>
                  </div>
                </div>
              ))}
            </div>

            {/* Logos de Plataformas */}
            <div className="text-center mb-6">
              <p className="text-sm font-semibold text-primary mb-4">PLATAFORMAS QUE CONFÍAN EN RAYUELA</p>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-6 items-center justify-items-center opacity-60 hover:opacity-80 transition-opacity">
                {[
                  { name: 'Servicios', icon: '🔧' },
                  { name: 'Freelance', icon: '💼' },
                  { name: 'B2B', icon: '🏢' },
                  { name: 'Local', icon: '📍' },
                ].map((platform) => (
                  <div
                    key={platform.name}
                    className="flex flex-col items-center justify-center p-3 bg-card border border-border rounded-lg hover:shadow-md transition-all group"
                  >
                    <span className="text-2xl mb-1 group-hover:scale-110 transition-transform">{platform.icon}</span>
                    <span className="text-xs font-medium text-muted-foreground text-center">{platform.name}</span>
                  </div>
                ))}
              </div>
            </div>

            <div className="text-center">
              <div className="inline-flex items-center gap-4 bg-primary/10 border border-primary/20 rounded-full px-6 py-3">
                <span className="text-primary text-lg">✓</span>
                <span className="text-primary font-semibold">Más de 150 marketplaces ya usan Rayuela</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* CTA Section */}
        <Card className="bg-gradient-to-r from-primary/10 to-primary/5 border-primary/20">
          <CardContent className="pt-8">
            <div className="text-center">
              <h3 className="text-2xl font-bold mb-4">
                ¿Listo para optimizar tu marketplace?
              </h3>
              <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
                Únete a marketplaces líderes que ya usan Rayuela para conectar mejor 
                a compradores y vendedores, aumentando transacciones y satisfacción.
              </p>
              <div className="flex gap-4 justify-center flex-wrap">
                <Button asChild size="lg">
                  <Link href="/register">
                    Empezar gratis
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Link>
                </Button>
                <Button variant="outline" asChild size="lg">
                  <Link href="/contact-sales">
                    Demo personalizada
                  </Link>
                </Button>
              </div>
              <p className="text-xs text-muted-foreground mt-4">
                Sin compromiso • Integración en 48 horas • Soporte especializado
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
