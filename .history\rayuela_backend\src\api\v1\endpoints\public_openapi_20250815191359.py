"""
Endpoint para servir la versión pública del OpenAPI.

SEGURIDAD CRÍTICA: Este endpoint proporciona una versión minimalista
del OpenAPI que oculta detalles de implementación de ML y protege la IP.
"""

from fastapi import APIRouter, Depends, Request
from fastapi.openapi.utils import get_openapi
from typing import Dict, Any
import json
from datetime import datetime

from src.core.deps import get_current_account_with_context
from src.db.models import Account
from src.core.logging import logger

router = APIRouter()


def create_public_openapi_spec() -> Dict[str, Any]:
    """
    Crea una especificación OpenAPI pública minimalista.
    
    SEGURIDAD: Esta función filtra endpoints sensibles y esquemas
    que podrían revelar la lógica de negocio interna.
    """
    
    # Endpoints permitidos en la versión pública
    PUBLIC_ENDPOINTS = {
        # Autenticación básica
        "/api/v1/auth/register",
        "/api/v1/auth/token", 
        "/api/v1/auth/logout",
        
        # Información de cuenta básica
        "/api/v1/accounts/current",
        "/api/v1/accounts/usage",
        
        # Gestión básica de usuarios finales
        "/api/v1/end-users/",
        "/api/v1/end-users/{user_id}",
        
        # Gestión básica de productos
        "/api/v1/products/",
        "/api/v1/products/{product_id}",
        "/api/v1/products/external/{external_id}",
        
        # Interacciones básicas
        "/api/v1/interactions/",
        "/api/v1/interactions/external",
        
        # Recomendaciones públicas (versión simplificada)
        "/api/v1/recommendations/personalized/query",
        "/api/v1/recommendations/products/{product_id}/similar",
        "/api/v1/recommendations/most-sold/",
        "/api/v1/recommendations/top-rated/",
        "/api/v1/recommendations/category/{category}",
        
        # Health check
        "/health"
    }
    
    # Esquemas permitidos en la versión pública
    PUBLIC_SCHEMAS = {
        # Autenticación
        "RegisterRequest", "RegisterResponse", "LoginRequest", "LoginResponse",
        
        # Modelos básicos
        "Account", "AccountResponse", "EndUser", "EndUserCreate", 
        "Product", "ProductCreate", "ProductUpdate", "Interaction", "InteractionCreate",
        
        # Esquemas públicos de recomendaciones
        "PublicRecommendationQueryRequest", "PublicRecommendationQueryExternalRequest",
        "PublicRecommendationGoal", "PublicModelVariant", "PublicExplanationLevel",
        
        # Esquemas de filtros y contexto (sin detalles sensibles)
        "FilterGroup", "Filter", "FilterOperator", "LogicalOperator",
        "RecommendationContext", "PageType", "DeviceType",
        
        # Respuestas paginadas
        "PaginatedResponse_Product_", "PaginatedResponse_EndUser_", "PaginatedResponse_Interaction_",
        
        # Errores estándar
        "HTTPValidationError", "ValidationError"
    }
    
    # Crear especificación base
    public_spec = {
        "openapi": "3.1.0",
        "info": {
            "title": "Rayuela Public API",
            "version": "v1",
            "description": """
# Rayuela Recommendation Engine - Public API

Esta es la API pública de Rayuela que proporciona funcionalidades esenciales 
para integrar recomendaciones personalizadas en su aplicación.

## Características Principales

- **Recomendaciones Personalizadas**: Algoritmos avanzados adaptados a sus usuarios
- **Gestión de Catálogo**: Administre productos y usuarios fácilmente  
- **Analytics Simplificados**: Métricas de rendimiento orientadas al negocio
- **Múltiples Estrategias**: Optimice para conversión, engagement o descubrimiento

## Autenticación

La API utiliza autenticación por API Key. Incluya su clave en el header:
```
X-API-Key: su_api_key_aqui
```

## Límites de Uso

Los límites dependen de su plan de suscripción. Consulte `/api/v1/accounts/usage` 
para ver su uso actual.

## Soporte

Para soporte técnico, contacte: <EMAIL>
            """.strip()
        },
        "paths": {},
        "components": {
            "schemas": {},
            "securitySchemes": {
                "APIKeyHeader": {
                    "type": "apiKey",
                    "in": "header",
                    "name": "X-API-Key",
                    "description": "API Key para autenticación"
                }
            }
        },
        "security": [{"APIKeyHeader": []}]
    }
    
    # Importar la especificación completa para filtrar
    from main import app
    full_spec = get_openapi(
        title=app.title,
        version=app.version,
        description=app.description,
        routes=app.routes,
    )
    
    # Filtrar endpoints públicos
    for path, methods in full_spec.get("paths", {}).items():
        if path in PUBLIC_ENDPOINTS:
            # Limpiar métodos sensibles si existen
            filtered_methods = {}
            for method, details in methods.items():
                if method.lower() in ["get", "post", "put", "patch", "delete"]:
                    # Limpiar detalles sensibles de la documentación
                    cleaned_details = clean_endpoint_details(details)
                    filtered_methods[method] = cleaned_details
            
            if filtered_methods:
                public_spec["paths"][path] = filtered_methods
    
    # Filtrar esquemas públicos
    for schema_name, schema_def in full_spec.get("components", {}).get("schemas", {}).items():
        if schema_name in PUBLIC_SCHEMAS:
            # Limpiar esquemas sensibles
            cleaned_schema = clean_schema_definition(schema_def)
            public_spec["components"]["schemas"][schema_name] = cleaned_schema
    
    return public_spec


def clean_endpoint_details(endpoint_details: Dict[str, Any]) -> Dict[str, Any]:
    """
    Limpia detalles sensibles de un endpoint.

    SEGURIDAD: Remueve información que podría revelar implementación interna.
    """
    cleaned = endpoint_details.copy()

    # Términos sensibles que deben ser removidos o reemplazados
    sensitive_terms = [
        "precision", "recall", "NDCG", "MAP", "collaborative", "content-based",
        "hybrid", "algorithm", "model_type", "strategy", "parameters"
    ]

    # Limpiar descripciones que mencionen detalles técnicos
    if "description" in cleaned:
        description = cleaned["description"]
        for term in sensitive_terms:
            description = description.replace(term, "algoritmo")
        cleaned["description"] = description

    # Limpiar parámetros sensibles
    if "parameters" in cleaned:
        filtered_params = []
        for param in cleaned["parameters"]:
            param_name = param.get("name", "")
            # Filtrar parámetros con nombres sensibles
            if param_name not in ["model_type", "strategy", "algorithm_type"]:
                # Limpiar descripción del parámetro
                if "description" in param:
                    param_desc = param["description"]
                    for term in sensitive_terms:
                        param_desc = param_desc.replace(term, "algoritmo")
                    param["description"] = param_desc
                filtered_params.append(param)
        cleaned["parameters"] = filtered_params

    # Limpiar ejemplos que contengan información sensible
    if "requestBody" in cleaned:
        request_body = cleaned["requestBody"]
        if "content" in request_body:
            for content_type, content_def in request_body["content"].items():
                if "schema" in content_def and "example" in content_def["schema"]:
                    # Usar ejemplos públicos seguros
                    content_def["schema"]["example"] = get_safe_example(content_def["schema"])

    return cleaned


def clean_schema_definition(schema_def: Dict[str, Any]) -> Dict[str, Any]:
    """
    Limpia definiciones de esquemas sensibles.
    
    SEGURIDAD: Remueve campos y ejemplos que podrían revelar IP.
    """
    cleaned = schema_def.copy()
    
    # Remover campos sensibles
    sensitive_fields = [
        "performance_metrics", "parameters", "model_type", "strategy",
        "precision", "recall", "ndcg", "map_score", "diversity", "novelty"
    ]
    
    if "properties" in cleaned:
        for field in sensitive_fields:
            if field in cleaned["properties"]:
                del cleaned["properties"][field]
    
    # Limpiar ejemplos
    if "example" in cleaned:
        cleaned["example"] = get_safe_example(cleaned)
    
    return cleaned


def get_safe_example(schema_def: Dict[str, Any]) -> Dict[str, Any]:
    """
    Genera ejemplos seguros sin información sensible.
    """
    safe_examples = {
        "RecommendationQueryRequest": {
            "user_id": 123,
            "model_variant": "standard",
            "recommendation_goal": "user_engagement",
            "include_explanation": True,
            "limit": 10
        },
        "Product": {
            "id": 1,
            "external_id": "prod_123",
            "name": "Ejemplo de Producto",
            "category": "electronics",
            "price": 99.99,
            "is_active": True
        }
    }
    
    # Retornar ejemplo seguro o uno genérico
    schema_name = schema_def.get("title", "")
    return safe_examples.get(schema_name, {"example": "safe_value"})


@router.get("/public-openapi.json")
async def get_public_openapi():
    """
    Endpoint para obtener la especificación OpenAPI pública.
    
    SEGURIDAD: Esta versión está diseñada para ser consumida públicamente
    sin revelar detalles de implementación o lógica de negocio interna.
    
    Returns:
        Dict: Especificación OpenAPI pública minimalista
    """
    return create_public_openapi_spec()


@router.get("/openapi.json")
async def get_private_openapi(
    account: Account = Depends(get_current_account_with_context)
):
    """
    Endpoint para obtener la especificación OpenAPI completa.
    
    SEGURIDAD: Requiere autenticación. Esta versión contiene todos los
    detalles técnicos para integración avanzada y desarrollo.
    
    Args:
        account: Cuenta autenticada requerida
        
    Returns:
        Dict: Especificación OpenAPI completa
    """
    from main import app
    from fastapi.openapi.utils import get_openapi
    
    # Retornar especificación completa para usuarios autenticados
    return get_openapi(
        title=f"{app.title} - Complete API",
        version=app.version,
        description=f"{app.description}\n\n**PRIVATE API**: Esta versión contiene todos los detalles técnicos.",
        routes=app.routes,
    )
