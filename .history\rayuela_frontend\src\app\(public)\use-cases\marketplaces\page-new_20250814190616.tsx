import React from "react";
import Link from "next/link";
import { generateMetadata as generateSEOMetadata } from '@/lib/seo';
import { ArrowLeft, Search, Clock, Users, Store, Zap } from "lucide-react";

export const metadata = generateSEOMetadata({
  title: 'Marketplaces - Casos de Uso | Rayuela',
  description: 'Optimiza la conexión entre oferta y demanda en tu marketplace. Reduce tiempo de búsqueda 25% y aumenta la satisfacción de compradores y vendedores.',
  path: '/use-cases/marketplaces',
  keywords: ['marketplace', 'búsqueda', 'matching', 'oferta', 'demanda', 'discovery'],
});

export default function MarketplacesUseCasePage() {
  return (
    <main className="bg-white">
      {/* Breadcrumb */}
      <section className="py-6 border-b border-gray-100">
        <div className="max-w-7xl mx-auto px-6">
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <Link href="/" className="hover:text-purple-600 transition-colors flex items-center gap-1">
              <ArrowLeft className="w-4 h-4" />
              Inicio
            </Link>
            <span>/</span>
            <span className="text-gray-900 font-medium">Marketplaces</span>
          </div>
        </div>
      </section>

      {/* Hero Section */}
      <section className="py-14 md:py-16">
        <div className="max-w-7xl mx-auto px-6">
          <div className="text-center mb-12">
            <div className="inline-flex items-center gap-2 bg-purple-50 text-purple-700 px-4 py-2 rounded-full text-sm font-medium mb-6">
              <span>🏪</span>{" "}
              Caso de Uso: Marketplaces
            </div>
            <h1 className="text-4xl md:text-5xl font-bold leading-tight mb-6">
              Conecta oferta y demanda de forma inteligente
            </h1>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto mb-8">
              Transforma tu marketplace en una plataforma que encuentra automáticamente lo que cada usuario busca,
              reduciendo fricción y aumentando transacciones.
            </p>
          </div>

          {/* Historia de Éxito */}
          <div className="card max-w-5xl mx-auto">
            <div className="flex items-start gap-4">
              <div className="w-16 h-16 bg-gradient-to-r from-purple-600 to-purple-700 rounded-xl flex items-center justify-center flex-shrink-0">
                <span className="text-white text-2xl">🏆</span>
              </div>
              <div className="flex-1">
                <h3 className="text-xl font-bold text-gray-900 mb-3">
                  Caso de Éxito: ServiceHub redujo tiempo de búsqueda 40% y aumentó GMV 25%
                </h3>
                <p className="text-gray-600 mb-6 leading-relaxed">
                  <strong>ServiceHub</strong>, un marketplace de servicios profesionales con 10,000 proveedores y 50,000 búsquedas mensuales, 
                  implementó Rayuela para mejorar el matching entre clientes y profesionales. En 90 días:
                </p>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                  <div className="bg-green-50 border border-green-200 rounded-lg p-4 text-center">
                    <div className="text-2xl font-bold text-green-600 mb-1">+25%</div>
                    <div className="text-sm text-gray-600">GMV total</div>
                  </div>
                  <div className="bg-purple-50 border border-purple-200 rounded-lg p-4 text-center">
                    <div className="text-2xl font-bold text-purple-600 mb-1">-40%</div>
                    <div className="text-sm text-gray-600">Tiempo búsqueda</div>
                  </div>
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 text-center">
                    <div className="text-2xl font-bold text-blue-600 mb-1">+60%</div>
                    <div className="text-sm text-gray-600">Matches exitosos</div>
                  </div>
                </div>
                <blockquote className="pl-4 border-l-4 border-purple-200 italic text-gray-600 bg-gray-50 p-4 rounded-r-lg">
                  "Rayuela transformó cómo nuestros usuarios encuentran servicios. Ahora conectamos automáticamente 
                  clientes con los profesionales más relevantes, no solo los mejor posicionados."
                  <footer className="mt-2 text-sm not-italic">
                    — <strong className="text-gray-900">Roberto Silva</strong>, CTO de ServiceHub
                  </footer>
                </blockquote>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Key Metrics */}
      <section className="section-gradient-strong">
        <div className="max-w-7xl mx-auto px-6">
          <div className="grid gap-6 md:grid-cols-3">
            <div className="card text-center">
              <Search className="w-12 h-12 text-green-600 mx-auto mb-4" />
              <div className="text-3xl font-bold text-green-600 mb-2">-25%</div>
              <p className="text-gray-600">Reducción en tiempo de búsqueda</p>
            </div>
            <div className="card text-center">
              <Zap className="w-12 h-12 text-purple-600 mx-auto mb-4" />
              <div className="text-3xl font-bold text-purple-600 mb-2">+35%</div>
              <p className="text-gray-600">Mejora en matching accuracy</p>
            </div>
            <div className="card text-center">
              <Users className="w-12 h-12 text-blue-600 mx-auto mb-4" />
              <div className="text-3xl font-bold text-blue-600 mb-2">+40%</div>
              <p className="text-gray-600">Incremento en transacciones</p>
            </div>
          </div>
        </div>
      </section>

      {/* Problem & Solution */}
      <section className="py-14 md:py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-6">
          <div className="grid gap-8 md:grid-cols-2">
            <div className="card border-red-200 bg-red-50">
              <h3 className="text-xl font-bold text-red-700 mb-4">El Problema</h3>
              <ul className="space-y-3 text-gray-600">
                <li>• <strong>Búsquedas ineficientes:</strong> Usuarios no encuentran lo que necesitan</li>
                <li>• <strong>Matching básico:</strong> Solo por categorías o palabras clave</li>
                <li>• <strong>Proveedores invisibles:</strong> Buenos servicios quedan ocultos</li>
                <li>• <strong>Abandono alto:</strong> Frustración por resultados irrelevantes</li>
                <li>• <strong>Competencia por ranking:</strong> Solo los primeros reciben tráfico</li>
              </ul>
            </div>

            <div className="card border-green-200 bg-green-50">
              <h3 className="text-xl font-bold text-green-700 mb-4">La Solución Rayuela</h3>
              <ul className="space-y-3 text-gray-600">
                <li>• <strong>Discovery inteligente:</strong> Encuentra servicios por intención, no solo keywords</li>
                <li>• <strong>Matching semántico:</strong> Conecta necesidades con capacidades reales</li>
                <li>• <strong>Visibilidad equitativa:</strong> Todos los proveedores tienen oportunidades</li>
                <li>• <strong>Experiencia fluida:</strong> Resultados relevantes desde la primera búsqueda</li>
                <li>• <strong>Optimización continua:</strong> Aprende de cada interacción exitosa</li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Tipos de Matching */}
      <section className="py-14 md:py-16">
        <div className="max-w-7xl mx-auto px-6">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Tipos de matching inteligente</h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Diferentes algoritmos para conectar oferta y demanda según el contexto
            </p>
          </div>
          
          <div className="card max-w-6xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Matching por Intención */}
              <div>
                <h4 className="font-bold text-lg mb-4">Matching por Intención</h4>
                <div className="space-y-4">
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div className="font-semibold text-blue-700 mb-2">Búsqueda: "Necesito renovar mi cocina"</div>
                    <div className="text-sm text-gray-600 space-y-1">
                      <div>✓ Arquitectos especializados en cocinas</div>
                      <div>✓ Contratistas con portfolio de renovaciones</div>
                      <div>✓ Diseñadores de interiores con experiencia</div>
                      <div>✓ Proveedores de electrodomésticos</div>
                    </div>
                  </div>
                  
                  <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                    <div className="font-semibold text-green-700 mb-2">Resultado</div>
                    <div className="text-sm text-gray-600">
                      Conecta automáticamente con profesionales que han completado proyectos similares,
                      no solo los que tienen "cocina" en su descripción.
                    </div>
                  </div>
                </div>
              </div>

              {/* Matching por Contexto */}
              <div>
                <h4 className="font-bold text-lg mb-4">Matching por Contexto</h4>
                <div className="space-y-4">
                  <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
                    <div className="font-semibold text-purple-700 mb-2">Contexto del Usuario</div>
                    <div className="text-sm text-gray-600 space-y-1">
                      <div>• Ubicación: Madrid centro</div>
                      <div>• Presupuesto: €5,000-€15,000</div>
                      <div>• Urgencia: 2-3 semanas</div>
                      <div>• Historial: Proyectos premium</div>
                    </div>
                  </div>
                  
                  <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
                    <div className="font-semibold text-orange-700 mb-2">Proveedores Recomendados</div>
                    <div className="text-sm text-gray-600">
                      Filtra automáticamente por disponibilidad, rango de precios, 
                      proximidad geográfica y calidad de trabajos anteriores.
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* ROI Calculator */}
      <section className="py-14 md:py-16 section-gradient-strong">
        <div className="max-w-7xl mx-auto px-6">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Calculadora de ROI para Marketplaces</h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Estima el impacto de mejorar el matching en tu plataforma
            </p>
          </div>
          
          <div className="card max-w-5xl mx-auto">
            <div className="grid gap-8 md:grid-cols-2">
              <div className="space-y-4">
                <h4 className="font-bold text-lg">Ejemplo: Marketplace con 1,000 transacciones/mes</h4>
                <div className="space-y-3 text-sm">
                  <div className="flex justify-between p-3 bg-gray-50 rounded">
                    <span>Transacciones mensuales:</span>
                    <span className="font-mono font-semibold">1,000</span>
                  </div>
                  <div className="flex justify-between p-3 bg-gray-50 rounded">
                    <span>Comisión promedio:</span>
                    <span className="font-mono font-semibold">8%</span>
                  </div>
                  <div className="flex justify-between p-3 bg-gray-50 rounded">
                    <span>Ticket promedio:</span>
                    <span className="font-mono font-semibold">€250</span>
                  </div>
                  <div className="flex justify-between p-3 bg-gray-100 rounded border-l-4 border-gray-400">
                    <span className="font-semibold">Revenue mensual actual:</span>
                    <span className="font-mono font-bold">€20,000</span>
                  </div>
                </div>
              </div>
              
              <div className="space-y-4">
                <h4 className="font-bold text-lg text-green-700">Con Rayuela (+35% transacciones):</h4>
                <div className="space-y-3 text-sm">
                  <div className="flex justify-between p-3 bg-green-50 rounded">
                    <span>Nuevas transacciones:</span>
                    <span className="font-mono font-semibold text-green-700">1,350</span>
                  </div>
                  <div className="flex justify-between p-3 bg-green-50 rounded">
                    <span>Mejor matching = mayor ticket:</span>
                    <span className="font-mono font-semibold text-green-700">€275</span>
                  </div>
                  <div className="flex justify-between p-3 bg-green-50 rounded">
                    <span>Revenue mensual nuevo:</span>
                    <span className="font-mono font-semibold text-green-700">€29,700</span>
                  </div>
                  <div className="flex justify-between p-3 bg-green-100 rounded border-l-4 border-green-500">
                    <span className="font-semibold">Incremento mensual:</span>
                    <span className="font-mono font-bold text-green-700">+€9,700</span>
                  </div>
                  <div className="flex justify-between p-3 bg-green-200 rounded border-l-4 border-green-600">
                    <span className="font-bold">ROI anual:</span>
                    <span className="font-mono font-bold text-green-800 text-lg">+€116,400</span>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="mt-8 p-6 bg-purple-50 rounded-lg border border-purple-200">
              <div className="text-center">
                <div className="text-purple-700 font-semibold mb-2">💡 Costo de Rayuela: €199/mes</div>
                <div className="text-purple-600 text-sm">
                  ROI de <strong>4,870%</strong> en el primer año • Recuperas la inversión en <strong>6 días</strong>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Final */}
      <section className="py-14 md:py-16 section-gradient-strong">
        <div className="max-w-4xl mx-auto px-6 text-center">
          <h2 className="text-3xl font-bold mb-4">
            ¿Listo para optimizar tu marketplace como ServiceHub?
          </h2>
          <p className="text-lg text-gray-600 mb-8 max-w-2xl mx-auto">
            Únete a decenas de marketplaces que ya están mejorando su matching 
            y aumentando transacciones con Rayuela.
          </p>
          <div className="flex flex-wrap gap-4 justify-center">
            <Link href="/register?utm_source=marketplaces-final" className="btn-primary">
              Empezar gratis ahora
            </Link>
            <Link href="/contact-sales?utm_source=marketplaces-final&industry=marketplace" className="btn-secondary">
              Hablar con experto en marketplaces
            </Link>
          </div>
          <p className="text-sm text-gray-500 mt-6">
            Sin compromiso • Integración en 48 horas • Matching mejorado en 7 días
          </p>
        </div>
      </section>
    </main>
  );
}
