# 🛠️ Guía de Implementación - Protección de IP

## Para Desarrolladores Internos

### Cómo Agregar Nuevos Endpoints Protegidos

#### 1. Definir Esquemas Públicos
```python
# En src/schemas/public_schemas.py
class PublicNewFeatureRequest(BaseModel):
    # Solo campos que pueden ser expuestos públicamente
    user_id: str
    preferences: PublicPreferences  # Usar esquemas públicos
    goal: PublicRecommendationGoal  # Mapear a términos públicos
    
    class Config:
        # Ejemplo seguro sin términos técnicos
        schema_extra = {
            "example": {
                "user_id": "user123",
                "preferences": {"category": "electronics"},
                "goal": "engagement"  # No "maximize_engagement"
            }
        }
```

#### 2. Implementar Conversión en Endpoints
```python
# En src/api/v1/endpoints/new_feature.py
from src.schemas.public_schemas import PublicNewFeatureRequest
from src.schemas.recommendations import NewFeatureRequest

@router.post("/new-feature")
async def new_feature_endpoint(
    request: PublicNewFeatureRequest,  # Esquema público
    account: Account = Depends(get_current_account_with_context)
):
    # Convertir de público a interno
    internal_request = NewFeatureRequest(
        user_id=request.user_id,
        preferences=convert_public_preferences(request.preferences),
        goal=PUBLIC_TO_INTERNAL_GOAL_MAPPING[request.goal]  # Conversión
    )
    
    # Procesar con lógica interna
    result = await process_new_feature(internal_request)
    
    # Convertir respuesta a formato público
    return convert_to_public_response(result)
```

#### 3. Agregar a Lista de Endpoints Públicos
```python
# En src/api/v1/endpoints/public_openapi.py
PUBLIC_ENDPOINTS = [
    "/api/v1/new-feature",  # Agregar aquí
    # ... otros endpoints
]
```

#### 4. Crear Tests de Protección
```python
# En tests/test_ip_protection.py
def test_new_feature_no_sensitive_terms(self):
    """Verificar que el nuevo endpoint no expone términos sensibles."""
    response = client.get("/api/v1/public-openapi.json")
    openapi_spec = response.json()
    
    # Buscar el endpoint en la especificación
    new_feature_path = openapi_spec["paths"].get("/api/v1/new-feature")
    assert new_feature_path is not None
    
    # Verificar que no contiene términos sensibles
    endpoint_str = json.dumps(new_feature_path)
    for term in SENSITIVE_TERMS:
        assert term not in endpoint_str.lower()
```

### Cómo Agregar Nuevos Términos Sensibles

#### 1. Identificar Términos a Proteger
```python
# Ejemplos de términos que deben protegerse:
NEW_SENSITIVE_TERMS = [
    "neural_network",      # Arquitectura de ML
    "embedding_dimension", # Parámetros técnicos
    "gradient_descent",    # Algoritmos de optimización
    "feature_engineering", # Técnicas de ML
    "cross_validation"     # Métodos de evaluación
]
```

#### 2. Actualizar Lista de Términos
```python
# En tests/test_ip_protection.py
SENSITIVE_TERMS = [
    # Términos existentes...
    "collaborative", "content-based", "hybrid",
    
    # Nuevos términos
    "neural_network",
    "embedding_dimension", 
    "gradient_descent",
    "feature_engineering",
    "cross_validation"
]
```

#### 3. Actualizar Función de Limpieza
```python
# En src/api/v1/endpoints/public_openapi.py
def clean_endpoint_details(endpoint_details: Dict[str, Any]) -> Dict[str, Any]:
    sensitive_terms = [
        # Términos existentes...
        "precision", "recall", "NDCG",
        
        # Nuevos términos
        "neural_network", "embedding_dimension",
        "gradient_descent", "feature_engineering"
    ]
    
    # Aplicar limpieza...
```

#### 4. Definir Reemplazos Apropiados
```python
# Mapeo de términos sensibles a públicos
TERM_REPLACEMENTS = {
    "neural_network": "advanced_algorithm",
    "embedding_dimension": "feature_size",
    "gradient_descent": "optimization",
    "feature_engineering": "data_processing",
    "cross_validation": "model_validation"
}
```

### Cómo Agregar Monitoreo de Seguridad

#### 1. Definir Nuevos Eventos de Seguridad
```python
# En src/utils/security_monitor.py
class SecurityEvent(str, Enum):
    PUBLIC_OPENAPI_ACCESS = "public_openapi_access"
    PRIVATE_OPENAPI_ACCESS = "private_openapi_access"
    SUSPICIOUS_PATTERN = "suspicious_pattern"
    NEW_SECURITY_EVENT = "new_security_event"  # Nuevo evento
```

#### 2. Implementar Logging de Seguridad
```python
def log_security_event(
    event_type: SecurityEvent,
    request: Request,
    account: Optional[Account] = None,
    additional_data: Optional[Dict] = None
):
    """Log de eventos de seguridad estandarizado."""
    
    base_data = {
        "client_ip": request.client.host if request.client else "unknown",
        "user_agent": request.headers.get("user-agent", "unknown"),
        "endpoint": str(request.url.path),
        "security_event": event_type.value,
        "timestamp": datetime.utcnow().isoformat(),
    }
    
    if account:
        base_data.update({
            "account_id": account.account_id,
            "account_name": account.name
        })
    
    if additional_data:
        base_data.update(additional_data)
    
    # Determinar nivel de log según criticidad
    if event_type in [SecurityEvent.PRIVATE_OPENAPI_ACCESS, SecurityEvent.SUSPICIOUS_PATTERN]:
        logger.warning(f"🔒 SECURITY ALERT: {event_type.value}", extra=base_data)
    else:
        logger.info(f"🌐 Security Event: {event_type.value}", extra=base_data)
```

#### 3. Usar en Endpoints
```python
@router.get("/sensitive-endpoint")
async def sensitive_endpoint(
    request: Request,
    account: Account = Depends(get_current_account_with_context)
):
    # Log del acceso
    log_security_event(
        SecurityEvent.NEW_SECURITY_EVENT,
        request,
        account,
        {"critical_action": True, "alert_level": "HIGH"}
    )
    
    # Lógica del endpoint...
```

## Para Desarrolladores Externos

### Usando la API Pública

#### 1. Obtener Especificación OpenAPI
```bash
# Obtener especificación pública (sin autenticación)
curl -X GET "https://api.rayuela.com/api/v1/public-openapi.json" \
  -H "Accept: application/json"
```

#### 2. Entender los Términos Públicos
```python
# Términos que verás en la API pública
PUBLIC_TERMS = {
    "goal": ["engagement", "discovery", "freshness", "balanced"],
    "model_variant": ["standard", "advanced", "premium"],
    "optimization": ["performance", "quality", "speed"]
}
```

#### 3. Hacer Requests con Términos Públicos
```python
import requests

# Request usando términos públicos
request_data = {
    "user_id": "user123",
    "goal": "engagement",        # No "maximize_engagement"
    "model_variant": "premium",  # No "hybrid"
    "limit": 10
}

response = requests.post(
    "https://api.rayuela.com/api/v1/recommendations",
    json=request_data,
    headers={"X-API-Key": "your-api-key"}
)
```

#### 4. Interpretar Respuestas
```python
# La respuesta usará términos públicos
response_data = {
    "recommendations": [...],
    "metadata": {
        "algorithm_type": "advanced",     # No detalles técnicos
        "optimization": "performance",    # Término genérico
        "quality_score": 0.85            # Sin métricas específicas
    }
}
```

### Mejores Prácticas para Integraciones

#### 1. Manejo de Errores
```python
def handle_api_response(response):
    if response.status_code == 401:
        # API key inválida o faltante
        raise AuthenticationError("Invalid API key")
    elif response.status_code == 429:
        # Rate limit excedido
        raise RateLimitError("Too many requests")
    elif response.status_code == 400:
        # Parámetros inválidos (posiblemente términos incorrectos)
        raise ValidationError("Invalid parameters")
```

#### 2. Validación de Parámetros
```python
def validate_public_parameters(goal, model_variant):
    """Validar que se usen solo términos públicos."""
    
    valid_goals = ["engagement", "discovery", "freshness", "balanced"]
    valid_variants = ["standard", "advanced", "premium"]
    
    if goal not in valid_goals:
        raise ValueError(f"Invalid goal. Use one of: {valid_goals}")
    
    if model_variant not in valid_variants:
        raise ValueError(f"Invalid model_variant. Use one of: {valid_variants}")
```

#### 3. Caching y Rate Limiting
```python
import time
from functools import lru_cache

class RayuelaClient:
    def __init__(self, api_key):
        self.api_key = api_key
        self.last_request_time = 0
        self.min_request_interval = 1.0  # 1 segundo entre requests
    
    def _rate_limit(self):
        """Implementar rate limiting del lado cliente."""
        elapsed = time.time() - self.last_request_time
        if elapsed < self.min_request_interval:
            time.sleep(self.min_request_interval - elapsed)
        self.last_request_time = time.time()
    
    @lru_cache(maxsize=100)
    def get_openapi_spec(self):
        """Cache de la especificación OpenAPI."""
        self._rate_limit()
        response = requests.get(
            "https://api.rayuela.com/api/v1/public-openapi.json"
        )
        return response.json()
```

## Troubleshooting

### Problemas Comunes

#### 1. "Invalid parameter" errors
```python
# ❌ Incorrecto - usando términos internos
request = {"goal": "maximize_engagement", "model_type": "collaborative"}

# ✅ Correcto - usando términos públicos  
request = {"goal": "engagement", "model_variant": "standard"}
```

#### 2. Rate limiting
```python
# Implementar backoff exponencial
import time
import random

def make_request_with_backoff(url, data, max_retries=3):
    for attempt in range(max_retries):
        response = requests.post(url, json=data)
        
        if response.status_code != 429:
            return response
            
        # Backoff exponencial con jitter
        delay = (2 ** attempt) + random.uniform(0, 1)
        time.sleep(delay)
    
    raise Exception("Max retries exceeded")
```

#### 3. Autenticación
```python
# Verificar que el API key esté en el header correcto
headers = {
    "X-API-Key": "your-api-key",  # No "Authorization"
    "Content-Type": "application/json"
}
```

### Contacto para Soporte

- **Documentación**: https://docs.rayuela.com
- **Soporte técnico**: <EMAIL>  
- **Slack de desarrolladores**: #rayuela-api-support
- **Issues de GitHub**: https://github.com/rayuela/api-issues

---

**Recuerda**: Siempre usa la especificación OpenAPI pública como referencia autorizada para los parámetros y términos correctos.
